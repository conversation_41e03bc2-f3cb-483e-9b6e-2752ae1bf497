<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<!-- <link rel="icon" type="image/svg+xml" href="/vite.svg" /> -->
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<!-- <script src="echarts.min.js"></script> -->
	<title>首页</title>
	<script src="/UE/ueditor.config.js" type="text/javascript" charset="utf-8"></script>
	<script src="/UE/ueditor.all.js" type="text/javascript" charset="utf-8"></script>
	<script src="/UE/lang/zh-cn/zh-cn.js" type="text/javascript" charset="utf-8"></script>
	<script src="/UE/ueditor.parse.min.js" type="text/javascript" charset="utf-8"></script>
	<!-- 线下 -->
	<!-- <script src="./public/UE/ueditor.config.js" type="text/javascript" charset="utf-8"></script> -->
	<!-- <script src="./public/UE/ueditor.all.js" type="text/javascript" charset="utf-8"></script> -->
	<!-- <script src="./public/UE/lang/zh-cn/zh-cn.js" type="text/javascript" charset="utf-8"></script> -->
	<!-- <script src="./public/UE/ueditor.parse.min.js" type="text/javascript" charset="utf-8"></script> -->
	<!-- 打包 -->
	<!-- <script src="./UE/ueditor.config.js" type="text/javascript" charset="utf-8"></script>
		<script src="./UE/ueditor.all.js" type="text/javascript" charset="utf-8"></script>
		<script src="./UE/lang/zh-cn/zh-cn.js" type="text/javascript" charset="utf-8"></script>
		<script src="./UE/ueditor.parse.min.js" type="text/javascript" charset="utf-8"></script> -->
</head>

<body>
	<div id="app"></div>
	<script type="module" src="/src/main.js"></script>
</body>
<script type="text/javascript">
		// let dev = window.location.href;
		// if(dev.indexOf('https') != -1){
		// 	const UConfig = document.createElement('script')
		// 	UConfig.type = 'text/javascript'
		// 	UConfig.charset = 'charset="utf-8'
		// 	UConfig.src = './UE/ueditor.config.js'
		// 	document.body.appendChild(UConfig)
		// 	const UAll = document.createElement('script')
		// 	UAll.type = 'text/javascript'
		// 	UAll.charset = 'charset="utf-8'
		// 	UAll.src = './UE/ueditor.all.js'
		// 	document.body.appendChild(UAll)
		// 	const ULang = document.createElement('script')
		// 	ULang.type = 'text/javascript'
		// 	ULang.charset = 'charset="utf-8'
		// 	ULang.src = './UE/lang/zh-cn/zh-cn.js'
		// 	document.body.appendChild(ULang)
		// 	const UEditor = document.createElement('script')
		// 	UEditor.type = 'text/javascript'
		// 	UEditor.charset = 'charset="utf-8'
		// 	UEditor.src = './UE/ueditor.all.js'
		// 	document.body.appendChild(UEditor)
		// }else{
		// 	console.log("kkk")
		// 	const ULang = document.createElement('script')
		// 	ULang.type = 'text/javascript'
		// 	ULang.charset = 'charset="utf-8'
		// 	ULang.src = './public/UE/lang/zh-cn/zh-cn.js'
		// 	document.body.appendChild(ULang)
		// 	const UConfig = document.createElement('script')
		// 	UConfig.type = 'text/javascript'
		// 	UConfig.charset = 'charset="utf-8'
		// 	UConfig.src = './public/UE/ueditor.config.js'
		// 	document.body.appendChild(UConfig)
		// 	const UAll = document.createElement('script')
		// 	UAll.type = 'text/javascript'
		// 	UAll.charset = 'charset="utf-8'
		// 	UAll.src = './public/UE/ueditor.all.js'
		// 	document.body.appendChild(UAll)
		// 	const UEditor = document.createElement('script')
		// 	UEditor.type = 'text/javascript'
		// 	UEditor.charset = 'charset="utf-8'
		// 	UEditor.src = './public/UE/ueditor.all.js'
		// 	document.body.appendChild(UEditor)
		// }
</script>

</html>