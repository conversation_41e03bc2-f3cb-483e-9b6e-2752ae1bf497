[2025-08-18T11:09:22+08:00][error] [2]Array to string conversion
[2025-08-18T11:09:22+08:00][error] Array to string conversion
[2025-08-18T11:09:22+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\cashier\Order.php(488): think\initializer\Error->appError(2, 'Array to string...', 'E:\\code\\diancan...', 488)
#1 [internal function]: app\shop\controller\store\cashier\Order->directOrder()
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\cashier\Order), Array)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\cashier\Order), Object(ReflectionMethod), Array)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#19 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#23 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#27 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#34 {main}
[2025-08-18T12:17:06+08:00][error] [0]Cannot access offset of type array on array
[2025-08-18T12:17:06+08:00][error] Cannot access offset of type array on array
[2025-08-18T12:17:06+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\model\order\Order.php(99): app\shop\model\order\Order->getOrderStatusText(Array)
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\model\order\Order.php(27): app\shop\model\order\Order->getStoreOrderList('all', Array)
#2 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\Order.php(25): app\shop\model\order\Order->getList('all', Array)
#3 [internal function]: app\shop\controller\store\Order->index('all')
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\Order), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\Order), Object(ReflectionMethod), Array)
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#21 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#25 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#29 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#35 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#36 {main}
[2025-08-18T12:17:14+08:00][error] [0]Cannot access offset of type array on array
[2025-08-18T12:17:14+08:00][error] Cannot access offset of type array on array
[2025-08-18T12:17:14+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\model\order\Order.php(99): app\shop\model\order\Order->getOrderStatusText(Array)
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\model\order\Order.php(27): app\shop\model\order\Order->getStoreOrderList('all', Array)
#2 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\Order.php(25): app\shop\model\order\Order->getList('all', Array)
#3 [internal function]: app\shop\controller\store\Order->index('all')
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\Order), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\Order), Object(ReflectionMethod), Array)
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#21 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#25 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#29 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#35 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#36 {main}
[2025-08-18T12:29:53+08:00][error] [10501]SQLSTATE[42S22]: Column not found: 1054 Unknown column 'op.product_num' in 'field list'
[2025-08-18T12:29:53+08:00][error] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'op.product_num' in 'field list'
[2025-08-18T12:29:53+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(1252): think\db\PDOConnection->getPDOStatement('SELECT SUM(`op`...', Array, false)
#1 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(1283): think\db\PDOConnection->value(Object(think\db\Query), 'SUM(`op`.`produ...', 0, false)
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\concern\AggregateQuery.php(35): think\db\PDOConnection->aggregate(Object(think\db\Query), 'SUM', 'SUM(`op`.`produ...', true, false)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\concern\AggregateQuery.php(88): think\db\BaseQuery->aggregate('SUM', 'op.product_num', true)
#4 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\model\order\Order.php(91): think\db\BaseQuery->sum('op.product_num')
#5 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\model\order\Order.php(27): app\shop\model\order\Order->getStoreOrderList('all', Array)
#6 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\Order.php(25): app\shop\model\order\Order->getList('all', Array)
#7 [internal function]: app\shop\controller\store\Order->index('all')
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\Order), Array)
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\Order), Object(ReflectionMethod), Array)
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#25 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#29 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#33 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#35 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#36 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#37 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#38 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#39 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#40 {main}
[2025-08-18T12:32:56+08:00][error] [10501]SQLSTATE[42S22]: Column not found: 1054 Unknown column 'op.product_num' in 'field list'
[2025-08-18T12:32:56+08:00][error] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'op.product_num' in 'field list'
[2025-08-18T12:32:56+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(1252): think\db\PDOConnection->getPDOStatement('SELECT SUM(`op`...', Array, false)
#1 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(1283): think\db\PDOConnection->value(Object(think\db\Query), 'SUM(`op`.`produ...', 0, false)
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\concern\AggregateQuery.php(35): think\db\PDOConnection->aggregate(Object(think\db\Query), 'SUM', 'SUM(`op`.`produ...', true, false)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\concern\AggregateQuery.php(88): think\db\BaseQuery->aggregate('SUM', 'op.product_num', true)
#4 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\model\order\Order.php(91): think\db\BaseQuery->sum('op.product_num')
#5 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\model\order\Order.php(27): app\shop\model\order\Order->getStoreOrderList('all', Array)
#6 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\Order.php(25): app\shop\model\order\Order->getList('all', Array)
#7 [internal function]: app\shop\controller\store\Order->index('all')
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\Order), Array)
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\Order), Object(ReflectionMethod), Array)
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#25 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#29 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#33 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#35 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#36 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#37 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#38 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#39 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#40 {main}
[2025-08-18T12:34:04+08:00][error] [10501]SQLSTATE[42S22]: Column not found: 1054 Unknown column 'op.product_num' in 'field list'
[2025-08-18T12:34:04+08:00][error] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'op.product_num' in 'field list'
[2025-08-18T12:34:04+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(1252): think\db\PDOConnection->getPDOStatement('SELECT SUM(`op`...', Array, false)
#1 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(1283): think\db\PDOConnection->value(Object(think\db\Query), 'SUM(`op`.`produ...', 0, false)
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\concern\AggregateQuery.php(35): think\db\PDOConnection->aggregate(Object(think\db\Query), 'SUM', 'SUM(`op`.`produ...', true, false)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\concern\AggregateQuery.php(88): think\db\BaseQuery->aggregate('SUM', 'op.product_num', true)
#4 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\model\order\Order.php(91): think\db\BaseQuery->sum('op.product_num')
#5 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\model\order\Order.php(27): app\shop\model\order\Order->getStoreOrderList('all', Array)
#6 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\Order.php(25): app\shop\model\order\Order->getList('all', Array)
#7 [internal function]: app\shop\controller\store\Order->index('all')
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\Order), Array)
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\Order), Object(ReflectionMethod), Array)
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#25 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#29 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#33 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#35 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#36 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#37 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#38 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#39 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#40 {main}
[2025-08-18T12:34:12+08:00][error] [10501]SQLSTATE[42S22]: Column not found: 1054 Unknown column 'op.product_num' in 'field list'
[2025-08-18T12:34:12+08:00][error] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'op.product_num' in 'field list'
[2025-08-18T12:34:12+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(1252): think\db\PDOConnection->getPDOStatement('SELECT SUM(`op`...', Array, false)
#1 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(1283): think\db\PDOConnection->value(Object(think\db\Query), 'SUM(`op`.`produ...', 0, false)
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\concern\AggregateQuery.php(35): think\db\PDOConnection->aggregate(Object(think\db\Query), 'SUM', 'SUM(`op`.`produ...', true, false)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\concern\AggregateQuery.php(88): think\db\BaseQuery->aggregate('SUM', 'op.product_num', true)
#4 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\model\order\Order.php(91): think\db\BaseQuery->sum('op.product_num')
#5 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\model\order\Order.php(27): app\shop\model\order\Order->getStoreOrderList('all', Array)
#6 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\Order.php(25): app\shop\model\order\Order->getList('all', Array)
#7 [internal function]: app\shop\controller\store\Order->index('all')
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\Order), Array)
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\Order), Object(ReflectionMethod), Array)
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#25 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#29 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#33 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#35 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#36 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#37 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#38 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#39 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#40 {main}
[2025-08-18T12:37:57+08:00][error] [0]Cannot access offset of type array on array
[2025-08-18T12:37:57+08:00][error] Cannot access offset of type array on array
[2025-08-18T12:37:57+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\model\order\Order.php(96): app\shop\model\order\Order->getOrderStatusText(Array)
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\model\order\Order.php(27): app\shop\model\order\Order->getStoreOrderList('all', Array)
#2 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\Order.php(25): app\shop\model\order\Order->getList('all', Array)
#3 [internal function]: app\shop\controller\store\Order->index('all')
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\Order), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\Order), Object(ReflectionMethod), Array)
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#21 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#25 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#29 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#35 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#36 {main}
[2025-08-18T12:38:03+08:00][error] [0]Cannot access offset of type array on array
[2025-08-18T12:38:03+08:00][error] Cannot access offset of type array on array
[2025-08-18T12:38:03+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\model\order\Order.php(96): app\shop\model\order\Order->getOrderStatusText(Array)
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\model\order\Order.php(27): app\shop\model\order\Order->getStoreOrderList('all', Array)
#2 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\Order.php(25): app\shop\model\order\Order->getList('all', Array)
#3 [internal function]: app\shop\controller\store\Order->index('all')
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\Order), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\Order), Object(ReflectionMethod), Array)
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#21 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#25 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#29 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#35 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#36 {main}
[2025-08-18T13:27:11+08:00][error] [0]date(): Argument #2 ($timestamp) must be of type ?int, string given
[2025-08-18T13:27:11+08:00][error] date(): Argument #2 ($timestamp) must be of type ?int, string given
[2025-08-18T13:27:11+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\Order.php(88): date('Y-m-d H:i:s', '2025-08-18 12:4...')
#1 [internal function]: app\shop\controller\store\Order->masterDetail('9')
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\Order), Array)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\Order), Object(ReflectionMethod), Array)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#19 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#23 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#27 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#34 {main}
[2025-08-18T13:27:14+08:00][error] [0]date(): Argument #2 ($timestamp) must be of type ?int, string given
[2025-08-18T13:27:14+08:00][error] date(): Argument #2 ($timestamp) must be of type ?int, string given
[2025-08-18T13:27:14+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\Order.php(88): date('Y-m-d H:i:s', '2025-08-18 12:4...')
#1 [internal function]: app\shop\controller\store\Order->masterDetail('9')
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\Order), Array)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\Order), Object(ReflectionMethod), Array)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#19 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#23 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#27 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#34 {main}
[2025-08-18T13:27:32+08:00][error] [0]date(): Argument #2 ($timestamp) must be of type ?int, string given
[2025-08-18T13:27:32+08:00][error] date(): Argument #2 ($timestamp) must be of type ?int, string given
[2025-08-18T13:27:32+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\Order.php(88): date('Y-m-d H:i:s', '2025-08-18 12:4...')
#1 [internal function]: app\shop\controller\store\Order->masterDetail('9')
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\Order), Array)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\Order), Object(ReflectionMethod), Array)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#19 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#23 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#27 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#34 {main}
[2025-08-18T13:28:08+08:00][error] [0]date(): Argument #2 ($timestamp) must be of type ?int, string given
[2025-08-18T13:28:08+08:00][error] date(): Argument #2 ($timestamp) must be of type ?int, string given
[2025-08-18T13:28:08+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\Order.php(88): date('Y-m-d H:i:s', '2025-08-18 12:4...')
#1 [internal function]: app\shop\controller\store\Order->masterDetail('9')
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\Order), Array)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\Order), Object(ReflectionMethod), Array)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#19 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#23 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#27 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#34 {main}
[2025-08-18T13:31:34+08:00][error] [0]array_column(): Argument #1 ($array) must be of type array, think\model\Collection given
[2025-08-18T13:31:34+08:00][error] array_column(): Argument #1 ($array) must be of type array, think\model\Collection given
[2025-08-18T13:31:34+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\api\service\order\DineInOrderService.php(51): array_column(Object(think\model\Collection), 'product_num')
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(106): app\api\service\order\DineInOrderService->settlement()
#2 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(76): app\api\controller\order\Order->handleDineInOrder(Array, Object(app\api\model\user\User), Object(think\model\Collection), Object(app\api\model\order\Cart))
#3 [internal function]: app\api\controller\order\Order->cart()
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\order\Order), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\order\Order), Object(ReflectionMethod), Array)
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#21 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#25 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#29 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#35 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#36 {main}
[2025-08-18T13:33:20+08:00][error] [2]Array to string conversion
[2025-08-18T13:33:20+08:00][error] Array to string conversion
[2025-08-18T13:33:20+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\Order.php(136): think\initializer\Error->appError(2, 'Array to string...', 'E:\\code\\diancan...', 136)
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\Order.php(93): app\shop\controller\store\Order->getOrderStatusText(Array)
#2 [internal function]: app\shop\controller\store\Order->masterDetail('9')
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\Order), Array)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\Order), Object(ReflectionMethod), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#20 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#24 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#28 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#34 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#35 {main}
[2025-08-18T13:33:51+08:00][error] [2]Array to string conversion
[2025-08-18T13:33:51+08:00][error] Array to string conversion
[2025-08-18T13:33:51+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\Order.php(136): think\initializer\Error->appError(2, 'Array to string...', 'E:\\code\\diancan...', 136)
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\Order.php(93): app\shop\controller\store\Order->getOrderStatusText(Array)
#2 [internal function]: app\shop\controller\store\Order->masterDetail('9')
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\Order), Array)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\Order), Object(ReflectionMethod), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#20 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#24 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#28 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#34 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#35 {main}
[2025-08-18T13:39:43+08:00][error] [0]array_column(): Argument #1 ($array) must be of type array, think\model\Collection given
[2025-08-18T13:39:43+08:00][error] array_column(): Argument #1 ($array) must be of type array, think\model\Collection given
[2025-08-18T13:39:43+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\api\service\order\DineInOrderService.php(51): array_column(Object(think\model\Collection), 'product_num')
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(106): app\api\service\order\DineInOrderService->settlement()
#2 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(76): app\api\controller\order\Order->handleDineInOrder(Array, Object(app\api\model\user\User), Object(think\model\Collection), Object(app\api\model\order\Cart))
#3 [internal function]: app\api\controller\order\Order->cart()
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\order\Order), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\order\Order), Object(ReflectionMethod), Array)
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#21 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#25 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#29 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#35 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#36 {main}
[2025-08-18T13:39:47+08:00][error] [0]array_column(): Argument #1 ($array) must be of type array, think\model\Collection given
[2025-08-18T13:39:47+08:00][error] array_column(): Argument #1 ($array) must be of type array, think\model\Collection given
[2025-08-18T13:39:47+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\api\service\order\DineInOrderService.php(51): array_column(Object(think\model\Collection), 'product_num')
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(106): app\api\service\order\DineInOrderService->settlement()
#2 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(76): app\api\controller\order\Order->handleDineInOrder(Array, Object(app\api\model\user\User), Object(think\model\Collection), Object(app\api\model\order\Cart))
#3 [internal function]: app\api\controller\order\Order->cart()
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\order\Order), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\order\Order), Object(ReflectionMethod), Array)
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#21 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#25 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#29 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#35 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#36 {main}
[2025-08-18T13:39:53+08:00][error] [0]array_column(): Argument #1 ($array) must be of type array, think\model\Collection given
[2025-08-18T13:39:53+08:00][error] array_column(): Argument #1 ($array) must be of type array, think\model\Collection given
[2025-08-18T13:39:53+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\api\service\order\DineInOrderService.php(51): array_column(Object(think\model\Collection), 'product_num')
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(106): app\api\service\order\DineInOrderService->settlement()
#2 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(76): app\api\controller\order\Order->handleDineInOrder(Array, Object(app\api\model\user\User), Object(think\model\Collection), Object(app\api\model\order\Cart))
#3 [internal function]: app\api\controller\order\Order->cart()
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\order\Order), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\order\Order), Object(ReflectionMethod), Array)
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#21 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#25 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#29 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#35 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#36 {main}
[2025-08-18T13:49:58+08:00][error] [0]Class "app\common\service\order\MasterOrderService" not found
[2025-08-18T13:49:58+08:00][error] Class "app\common\service\order\MasterOrderService" not found
[2025-08-18T13:49:58+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(76): app\api\controller\order\Order->handleDineInOrder(Array, Object(app\api\model\user\User), Object(think\model\Collection), Object(app\api\model\order\Cart))
#1 [internal function]: app\api\controller\order\Order->cart()
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\order\Order), Array)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\order\Order), Object(ReflectionMethod), Array)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#19 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#23 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#27 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#34 {main}
[2025-08-18T13:51:53+08:00][error] [0]Class "app\common\service\order\MasterOrderService" not found
[2025-08-18T13:51:53+08:00][error] Class "app\common\service\order\MasterOrderService" not found
[2025-08-18T13:51:53+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(76): app\api\controller\order\Order->handleDineInOrder(Array, Object(app\api\model\user\User), Object(think\model\Collection), Object(app\api\model\order\Cart))
#1 [internal function]: app\api\controller\order\Order->cart()
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\order\Order), Array)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\order\Order), Object(ReflectionMethod), Array)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#19 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#23 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#27 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#34 {main}
[2025-08-18T13:58:55+08:00][error] [0]Access to undeclared static property app\api\service\order\DineInOrderService::$app_id
[2025-08-18T13:58:55+08:00][error] Access to undeclared static property app\api\service\order\DineInOrderService::$app_id
[2025-08-18T13:58:55+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(1521): app\api\service\order\DineInOrderService->app\api\service\order\{closure}(Object(think\db\connector\Mysql))
#1 [internal function]: think\db\PDOConnection->transaction(Object(Closure))
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\DbManager.php(409): call_user_func_array(Array, Array)
#3 [internal function]: think\DbManager->__call('transaction', Array)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Facade.php(97): call_user_func_array(Array, Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\app\api\service\order\DineInOrderService.php(98): think\Facade::__callStatic('transaction', Array)
#6 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(152): app\api\service\order\DineInOrderService->createSubOrder(Array)
#7 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(76): app\api\controller\order\Order->handleDineInOrder(Array, Object(app\api\model\user\User), Object(think\model\Collection), Object(app\api\model\order\Cart))
#8 [internal function]: app\api\controller\order\Order->cart()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\order\Order), Array)
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\order\Order), Object(ReflectionMethod), Array)
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#26 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#30 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#34 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#35 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#36 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#37 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#38 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#39 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#40 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#41 {main}
[2025-08-18T14:08:37+08:00][error] [2]Undefined array key 0
[2025-08-18T14:08:37+08:00][error] Undefined array key 0
[2025-08-18T14:08:37+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\Builder.php(81): think\initializer\Error->appError(2, 'Undefined array...', 'E:\\code\\diancan...', 81)
#1 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\builder\Mysql.php(129): think\db\Builder->parseData(Object(think\db\Query), Array)
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(986): think\db\builder\Mysql->insert(Object(think\db\Query))
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\BaseQuery.php(1166): think\db\PDOConnection->insert(Object(think\db\Query), true)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\Model.php(714): think\db\BaseQuery->insert(Array, true)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(1521): think\Model->think\{closure}(Object(think\db\connector\Mysql))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\concern\Transaction.php(47): think\db\PDOConnection->transaction(Object(Closure))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\Model.php(709): think\db\BaseQuery->transaction(Object(Closure))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\Model.php(550): think\Model->insertData(NULL)
#9 E:\code\diancan\jjjfood\jjj_food_chain\app\api\service\order\DineInOrderService.php(110): think\Model->save(Array)
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(1521): app\api\service\order\DineInOrderService->app\api\service\order\{closure}(Object(think\db\connector\Mysql))
#11 [internal function]: think\db\PDOConnection->transaction(Object(Closure))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\DbManager.php(409): call_user_func_array(Array, Array)
#13 [internal function]: think\DbManager->__call('transaction', Array)
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Facade.php(97): call_user_func_array(Array, Array)
#15 E:\code\diancan\jjjfood\jjj_food_chain\app\api\service\order\DineInOrderService.php(99): think\Facade::__callStatic('transaction', Array)
#16 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(166): app\api\service\order\DineInOrderService->createSubOrder(Array)
#17 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(76): app\api\controller\order\Order->handleDineInOrder(Array, Object(app\api\model\user\User), Object(think\model\Collection), Object(app\api\model\order\Cart))
#18 [internal function]: app\api\controller\order\Order->cart()
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\order\Order), Array)
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\order\Order), Object(ReflectionMethod), Array)
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#35 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#36 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#37 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#38 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#39 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#40 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#41 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#42 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#43 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#44 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#45 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#46 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#47 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#48 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#49 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#50 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#51 {main}
[2025-08-18T14:10:32+08:00][error] [2]Undefined array key 0
[2025-08-18T14:10:32+08:00][error] Undefined array key 0
[2025-08-18T14:10:32+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\Builder.php(81): think\initializer\Error->appError(2, 'Undefined array...', 'E:\\code\\diancan...', 81)
#1 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\builder\Mysql.php(129): think\db\Builder->parseData(Object(think\db\Query), Array)
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(986): think\db\builder\Mysql->insert(Object(think\db\Query))
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\BaseQuery.php(1166): think\db\PDOConnection->insert(Object(think\db\Query), true)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\Model.php(714): think\db\BaseQuery->insert(Array, true)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(1521): think\Model->think\{closure}(Object(think\db\connector\Mysql))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\concern\Transaction.php(47): think\db\PDOConnection->transaction(Object(Closure))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\Model.php(709): think\db\BaseQuery->transaction(Object(Closure))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\Model.php(550): think\Model->insertData(NULL)
#9 E:\code\diancan\jjjfood\jjj_food_chain\app\api\service\order\DineInOrderService.php(110): think\Model->save(Array)
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(1521): app\api\service\order\DineInOrderService->app\api\service\order\{closure}(Object(think\db\connector\Mysql))
#11 [internal function]: think\db\PDOConnection->transaction(Object(Closure))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\DbManager.php(409): call_user_func_array(Array, Array)
#13 [internal function]: think\DbManager->__call('transaction', Array)
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Facade.php(97): call_user_func_array(Array, Array)
#15 E:\code\diancan\jjjfood\jjj_food_chain\app\api\service\order\DineInOrderService.php(99): think\Facade::__callStatic('transaction', Array)
#16 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(166): app\api\service\order\DineInOrderService->createSubOrder(Array)
#17 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(76): app\api\controller\order\Order->handleDineInOrder(Array, Object(app\api\model\user\User), Object(think\model\Collection), Object(app\api\model\order\Cart))
#18 [internal function]: app\api\controller\order\Order->cart()
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\order\Order), Array)
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\order\Order), Object(ReflectionMethod), Array)
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#35 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#36 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#37 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#38 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#39 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#40 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#41 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#42 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#43 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#44 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#45 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#46 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#47 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#48 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#49 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#50 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#51 {main}
[2025-08-18T14:13:20+08:00][error] [10501]SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'image_id' cannot be null
[2025-08-18T14:13:20+08:00][error] SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'image_id' cannot be null
[2025-08-18T14:13:20+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(899): think\db\PDOConnection->getPDOStatement('INSERT INTO `jj...', Array, true, false)
#1 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(860): think\db\PDOConnection->queryPDOStatement(Object(think\db\Query), 'INSERT INTO `jj...')
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(989): think\db\PDOConnection->pdoExecute(Object(think\db\Query), 'INSERT INTO `jj...')
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\BaseQuery.php(1166): think\db\PDOConnection->insert(Object(think\db\Query), true)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\Model.php(714): think\db\BaseQuery->insert(Array, true)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(1521): think\Model->think\{closure}(Object(think\db\connector\Mysql))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\concern\Transaction.php(47): think\db\PDOConnection->transaction(Object(Closure))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\Model.php(709): think\db\BaseQuery->transaction(Object(Closure))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\Model.php(550): think\Model->insertData(NULL)
#9 E:\code\diancan\jjjfood\jjj_food_chain\app\api\service\order\DineInOrderService.php(175): think\Model->save(Array)
#10 E:\code\diancan\jjjfood\jjj_food_chain\app\api\service\order\DineInOrderService.php(135): app\api\service\order\DineInOrderService->createOrderProducts(25)
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\db\PDOConnection.php(1521): app\api\service\order\DineInOrderService->app\api\service\order\{closure}(Object(think\db\connector\Mysql))
#12 [internal function]: think\db\PDOConnection->transaction(Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\DbManager.php(409): call_user_func_array(Array, Array)
#14 [internal function]: think\DbManager->__call('transaction', Array)
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Facade.php(97): call_user_func_array(Array, Array)
#16 E:\code\diancan\jjjfood\jjj_food_chain\app\api\service\order\DineInOrderService.php(99): think\Facade::__callStatic('transaction', Array)
#17 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(161): app\api\service\order\DineInOrderService->createSubOrder(10)
#18 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(76): app\api\controller\order\Order->handleDineInOrder(Array, Object(app\api\model\user\User), Object(think\model\Collection), Object(app\api\model\order\Cart))
#19 [internal function]: app\api\controller\order\Order->cart()
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\order\Order), Array)
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\order\Order), Object(ReflectionMethod), Array)
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#35 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#36 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#37 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#38 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#39 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#40 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#41 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#42 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#43 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#44 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#45 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#46 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#47 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#48 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#49 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#50 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#51 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#52 {main}
[2025-08-18T14:23:26+08:00][error] [2]Undefined array key 20
[2025-08-18T14:23:26+08:00][error] Undefined array key 20
[2025-08-18T14:23:26+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\common\model\order\Order.php(169): think\initializer\Error->appError(2, 'Undefined array...', 'E:\\code\\diancan...', 169)
#1 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Attribute.php(534): app\common\model\order\Order->getOrderSourceTextAttr(NULL, Array)
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Attribute.php(492): think\Model->getValue('order_source_te...', NULL, false)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Conversion.php(309): think\Model->getAttr('order_source_te...')
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Conversion.php(252): think\Model->appendAttrToArray(Array, 1, 'order_source_te...', Array, Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-helper\src\Collection.php(59): think\Model->toArray()
#6 [internal function]: think\Collection->think\{closure}(Object(app\common\model\order\Order))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-helper\src\Collection.php(58): array_map(Object(Closure), Array)
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Conversion.php(268): think\Collection->toArray()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Conversion.php(377): think\Model->toArray()
#10 [internal function]: think\Model->jsonSerialize()
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\response\Json.php(47): json_encode(Array, 256)
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Response.php(389): think\response\Json->output(Array)
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Response.php(131): think\Response->getContent()
#14 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(22): think\Response->send()
#15 {main}
[2025-08-18T14:23:37+08:00][error] [2]Undefined array key 20
[2025-08-18T14:23:37+08:00][error] Undefined array key 20
[2025-08-18T14:23:37+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\common\model\order\Order.php(169): think\initializer\Error->appError(2, 'Undefined array...', 'E:\\code\\diancan...', 169)
#1 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Attribute.php(534): app\common\model\order\Order->getOrderSourceTextAttr(NULL, Array)
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Attribute.php(492): think\Model->getValue('order_source_te...', NULL, false)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Conversion.php(309): think\Model->getAttr('order_source_te...')
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Conversion.php(252): think\Model->appendAttrToArray(Array, 1, 'order_source_te...', Array, Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-helper\src\Collection.php(59): think\Model->toArray()
#6 [internal function]: think\Collection->think\{closure}(Object(app\shop\model\order\Order))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-helper\src\Collection.php(58): array_map(Object(Closure), Array)
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-helper\src\Collection.php(628): think\Collection->toArray()
#9 [internal function]: think\Collection->jsonSerialize()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\response\Json.php(47): json_encode(Array, 256)
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Response.php(389): think\response\Json->output(Array)
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Response.php(131): think\Response->getContent()
#13 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(22): think\Response->send()
#14 {main}
[2025-08-18T14:23:54+08:00][error] [2]Undefined array key 20
[2025-08-18T14:23:54+08:00][error] Undefined array key 20
[2025-08-18T14:23:54+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\common\model\order\Order.php(169): think\initializer\Error->appError(2, 'Undefined array...', 'E:\\code\\diancan...', 169)
#1 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Attribute.php(534): app\common\model\order\Order->getOrderSourceTextAttr(NULL, Array)
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Attribute.php(492): think\Model->getValue('order_source_te...', NULL, false)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Conversion.php(309): think\Model->getAttr('order_source_te...')
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Conversion.php(252): think\Model->appendAttrToArray(Array, 1, 'order_source_te...', Array, Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-helper\src\Collection.php(59): think\Model->toArray()
#6 [internal function]: think\Collection->think\{closure}(Object(app\shop\model\order\Order))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-helper\src\Collection.php(58): array_map(Object(Closure), Array)
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-helper\src\Collection.php(628): think\Collection->toArray()
#9 [internal function]: think\Collection->jsonSerialize()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\response\Json.php(47): json_encode(Array, 256)
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Response.php(389): think\response\Json->output(Array)
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Response.php(131): think\Response->getContent()
#13 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(22): think\Response->send()
#14 {main}
[2025-08-18T14:24:12+08:00][error] [2]Undefined array key 20
[2025-08-18T14:24:12+08:00][error] Undefined array key 20
[2025-08-18T14:24:12+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\common\model\order\Order.php(169): think\initializer\Error->appError(2, 'Undefined array...', 'E:\\code\\diancan...', 169)
#1 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Attribute.php(534): app\common\model\order\Order->getOrderSourceTextAttr(NULL, Array)
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Attribute.php(492): think\Model->getValue('order_source_te...', NULL, false)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Conversion.php(309): think\Model->getAttr('order_source_te...')
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Conversion.php(252): think\Model->appendAttrToArray(Array, 1, 'order_source_te...', Array, Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-helper\src\Collection.php(59): think\Model->toArray()
#6 [internal function]: think\Collection->think\{closure}(Object(app\shop\model\order\Order))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-helper\src\Collection.php(58): array_map(Object(Closure), Array)
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-helper\src\Collection.php(628): think\Collection->toArray()
#9 [internal function]: think\Collection->jsonSerialize()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\response\Json.php(47): json_encode(Array, 256)
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Response.php(389): think\response\Json->output(Array)
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Response.php(131): think\Response->getContent()
#13 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(22): think\Response->send()
#14 {main}
[2025-08-18T14:25:27+08:00][error] [2]Undefined array key 20
[2025-08-18T14:25:27+08:00][error] Undefined array key 20
[2025-08-18T14:25:27+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\common\model\order\Order.php(169): think\initializer\Error->appError(2, 'Undefined array...', 'E:\\code\\diancan...', 169)
#1 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Attribute.php(534): app\common\model\order\Order->getOrderSourceTextAttr(NULL, Array)
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Attribute.php(492): think\Model->getValue('order_source_te...', NULL, false)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Conversion.php(309): think\Model->getAttr('order_source_te...')
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Conversion.php(252): think\Model->appendAttrToArray(Array, 1, 'order_source_te...', Array, Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-helper\src\Collection.php(59): think\Model->toArray()
#6 [internal function]: think\Collection->think\{closure}(Object(app\shop\model\order\Order))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-helper\src\Collection.php(58): array_map(Object(Closure), Array)
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-helper\src\Collection.php(628): think\Collection->toArray()
#9 [internal function]: think\Collection->jsonSerialize()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\response\Json.php(47): json_encode(Array, 256)
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Response.php(389): think\response\Json->output(Array)
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Response.php(131): think\Response->getContent()
#13 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(22): think\Response->send()
#14 {main}
[2025-08-18T14:25:34+08:00][error] [2]Undefined array key 20
[2025-08-18T14:25:34+08:00][error] Undefined array key 20
[2025-08-18T14:25:34+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\common\model\order\Order.php(169): think\initializer\Error->appError(2, 'Undefined array...', 'E:\\code\\diancan...', 169)
#1 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Attribute.php(534): app\common\model\order\Order->getOrderSourceTextAttr(NULL, Array)
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Attribute.php(492): think\Model->getValue('order_source_te...', NULL, false)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Conversion.php(309): think\Model->getAttr('order_source_te...')
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-orm\src\model\concern\Conversion.php(252): think\Model->appendAttrToArray(Array, 1, 'order_source_te...', Array, Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-helper\src\Collection.php(59): think\Model->toArray()
#6 [internal function]: think\Collection->think\{closure}(Object(app\shop\model\order\Order))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-helper\src\Collection.php(58): array_map(Object(Closure), Array)
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-helper\src\Collection.php(628): think\Collection->toArray()
#9 [internal function]: think\Collection->jsonSerialize()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\response\Json.php(47): json_encode(Array, 256)
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Response.php(389): think\response\Json->output(Array)
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Response.php(131): think\Response->getContent()
#13 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(22): think\Response->send()
#14 {main}
[2025-08-18T14:41:05+08:00][error] method not exists:app\api\controller\order\Order->detail()
[2025-08-18T14:41:05+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#1 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#15 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#19 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#23 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#29 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#30 {main}
[2025-08-18T14:52:25+08:00][error] [0]Class "app\common\model\order\OrderStatusEnum" not found
[2025-08-18T14:52:25+08:00][error] Class "app\common\model\order\OrderStatusEnum" not found
[2025-08-18T14:52:25+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\common\model\order\MasterOrder.php(242): app\common\model\order\Order->getOrderStatusText()
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\cashier\Index.php(336): app\common\model\order\MasterOrder::getDetailWithProducts('10')
#2 [internal function]: app\shop\controller\store\cashier\Index->detail()
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\cashier\Index), Array)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\cashier\Index), Object(ReflectionMethod), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#20 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#24 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#28 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#34 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#35 {main}
[2025-08-18T14:53:22+08:00][error] [0]Class "app\common\model\order\OrderStatusEnum" not found
[2025-08-18T14:53:22+08:00][error] Class "app\common\model\order\OrderStatusEnum" not found
[2025-08-18T14:53:22+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\common\model\order\MasterOrder.php(242): app\common\model\order\Order->getOrderStatusText()
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\cashier\Index.php(336): app\common\model\order\MasterOrder::getDetailWithProducts('10')
#2 [internal function]: app\shop\controller\store\cashier\Index->detail()
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\cashier\Index), Array)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\cashier\Index), Object(ReflectionMethod), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#20 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#24 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#28 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#34 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#35 {main}
[2025-08-18T15:12:35+08:00][error] [0]Call to undefined method think\model\Collection::sum()
[2025-08-18T15:12:35+08:00][error] Call to undefined method think\model\Collection::sum()
[2025-08-18T15:12:35+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\api\model\order\Order.php(89): app\api\model\order\Order->getDineInMasterOrders(10004, Array)
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\user\Order.php(35): app\api\model\order\Order->getList(10004, Array)
#2 [internal function]: app\api\controller\user\Order->lists()
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\user\Order), Array)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\user\Order), Object(ReflectionMethod), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#20 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#24 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#28 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#34 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#35 {main}
[2025-08-18T15:12:46+08:00][error] [0]Call to undefined method think\model\Collection::sum()
[2025-08-18T15:12:46+08:00][error] Call to undefined method think\model\Collection::sum()
[2025-08-18T15:12:46+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\api\model\order\Order.php(89): app\api\model\order\Order->getDineInMasterOrders(10004, Array)
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\user\Order.php(35): app\api\model\order\Order->getList(10004, Array)
#2 [internal function]: app\api\controller\user\Order->lists()
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\user\Order), Array)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\user\Order), Object(ReflectionMethod), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#20 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#24 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#28 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#34 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#35 {main}
[2025-08-18T15:16:32+08:00][error] [0]Call to undefined method think\model\Collection::sum()
[2025-08-18T15:16:32+08:00][error] Call to undefined method think\model\Collection::sum()
[2025-08-18T15:16:32+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\api\model\order\Order.php(89): app\api\model\order\Order->getDineInMasterOrders(10004, Array)
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\user\Order.php(35): app\api\model\order\Order->getList(10004, Array)
#2 [internal function]: app\api\controller\user\Order->lists()
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\user\Order), Array)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\user\Order), Object(ReflectionMethod), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#20 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#24 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#28 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#34 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#35 {main}
[2025-08-18T15:16:55+08:00][error] [0]Call to undefined method think\model\Collection::sum()
[2025-08-18T15:16:55+08:00][error] Call to undefined method think\model\Collection::sum()
[2025-08-18T15:16:55+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\api\model\order\Order.php(89): app\api\model\order\Order->getDineInMasterOrders(10004, Array)
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\user\Order.php(35): app\api\model\order\Order->getList(10004, Array)
#2 [internal function]: app\api\controller\user\Order->lists()
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\user\Order), Array)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\user\Order), Object(ReflectionMethod), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#20 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#24 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#28 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#34 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#35 {main}
[2025-08-18T15:17:55+08:00][error] [64]Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:17:55+08:00][error] Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:17:55+08:00][error] #0 [internal function]: think\initializer\Error->appShutdown()
#1 {main}
[2025-08-18T15:18:18+08:00][error] [64]Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:18:18+08:00][error] Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:18:18+08:00][error] #0 [internal function]: think\initializer\Error->appShutdown()
#1 {main}
[2025-08-18T15:20:05+08:00][error] [64]Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:20:05+08:00][error] Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:20:05+08:00][error] #0 [internal function]: think\initializer\Error->appShutdown()
#1 {main}
[2025-08-18T15:20:07+08:00][error] [64]Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:20:07+08:00][error] Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:20:07+08:00][error] #0 [internal function]: think\initializer\Error->appShutdown()
#1 {main}
[2025-08-18T15:20:10+08:00][error] [64]Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:20:10+08:00][error] Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:20:10+08:00][error] #0 [internal function]: think\initializer\Error->appShutdown()
#1 {main}
[2025-08-18T15:20:11+08:00][error] [64]Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:20:11+08:00][error] Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:20:11+08:00][error] #0 [internal function]: think\initializer\Error->appShutdown()
#1 {main}
[2025-08-18T15:20:12+08:00][error] [64]Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:20:12+08:00][error] Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:20:12+08:00][error] #0 [internal function]: think\initializer\Error->appShutdown()
#1 {main}
[2025-08-18T15:20:13+08:00][error] [64]Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:20:13+08:00][error] Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:20:13+08:00][error] #0 [internal function]: think\initializer\Error->appShutdown()
#1 {main}
[2025-08-18T15:22:41+08:00][error] [64]Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:22:41+08:00][error] Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:22:41+08:00][error] #0 [internal function]: think\initializer\Error->appShutdown()
#1 {main}
[2025-08-18T15:24:37+08:00][error] [0]Class "think\Paginate" not found
[2025-08-18T15:24:37+08:00][error] Class "think\Paginate" not found
[2025-08-18T15:24:37+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\user\Order.php(35): app\api\model\order\Order->getList(10004, Array)
#1 [internal function]: app\api\controller\user\Order->lists()
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\user\Order), Array)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\user\Order), Object(ReflectionMethod), Array)
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#19 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#23 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#27 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#34 {main}
[2025-08-18T15:25:03+08:00][error] [64]Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:25:03+08:00][error] Access level to app\shop\model\order\Order::getOrderStatusText() must be public (as in class app\common\model\order\Order)
[2025-08-18T15:25:03+08:00][error] #0 [internal function]: think\initializer\Error->appShutdown()
#1 {main}
[2025-08-18T16:48:54+08:00][error] [0]array_column(): Argument #1 ($array) must be of type array, think\model\Collection given
[2025-08-18T16:48:54+08:00][error] array_column(): Argument #1 ($array) must be of type array, think\model\Collection given
[2025-08-18T16:48:54+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\api\service\order\DineInOrderService.php(52): array_column(Object(think\model\Collection), 'product_num')
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(106): app\api\service\order\DineInOrderService->settlement()
#2 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(76): app\api\controller\order\Order->handleDineInOrder(Array, Object(app\api\model\user\User), Object(think\model\Collection), Object(app\api\model\order\Cart))
#3 [internal function]: app\api\controller\order\Order->cart()
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\order\Order), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\order\Order), Object(ReflectionMethod), Array)
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#21 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#25 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#29 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#35 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#36 {main}
[2025-08-18T16:49:03+08:00][error] [0]array_column(): Argument #1 ($array) must be of type array, think\model\Collection given
[2025-08-18T16:49:03+08:00][error] array_column(): Argument #1 ($array) must be of type array, think\model\Collection given
[2025-08-18T16:49:03+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\app\api\service\order\DineInOrderService.php(52): array_column(Object(think\model\Collection), 'product_num')
#1 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(106): app\api\service\order\DineInOrderService->settlement()
#2 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\order\Order.php(76): app\api\controller\order\Order->handleDineInOrder(Array, Object(app\api\model\user\User), Object(think\model\Collection), Object(app\api\model\order\Cart))
#3 [internal function]: app\api\controller\order\Order->cart()
#4 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\order\Order), Array)
#5 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\order\Order), Object(ReflectionMethod), Array)
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#21 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#25 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#29 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#35 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#36 {main}
