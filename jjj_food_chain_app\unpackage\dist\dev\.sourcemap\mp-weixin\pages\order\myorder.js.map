{"version": 3, "file": "myorder.js", "sources": ["pages/order/myorder.vue", "D:/HBuilderX.3.4.18.20220630/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvb3JkZXIvbXlvcmRlci52dWU"], "sourcesContent": ["<template>\r\n\t<view :data-theme=\"theme()\" :class=\"theme() || ''\">\r\n\t\t<view class=\"top-tabbar d-a-c\">\r\n\t\t\t<view :class=\"dataType == 1 ? 'tab-item active' : 'tab-item'\" @click=\"orderStateFunc(1)\">当前订单</view>\r\n\t\t\t<view :class=\"dataType == 2 ? 'tab-item active' : 'tab-item'\" @click=\"orderStateFunc(2)\">历史订单</view>\r\n\t\t</view>\r\n\t\t<!--列表-->\r\n\t\t<view class=\"order-list\">\r\n\t\t\t<view class=\"item\" v-for=\"(item, index) in listData\" :key=\"index\" @click.stop=\"gotoOrder(item)\">\r\n\t\t\t\t<view class=\"d-b-c pb14\">\r\n\t\t\t\t\t<text class=\"order-tips\">{{item.order_label}}</text>\r\n\t\t\t\t\t<view class=\"item-dianpu flex-1\">\r\n\t\t\t\t\t\t<view class=\"item-d-l mr10\">\r\n\t\t\t\t\t\t\t<text class=\"gray3 f28 fb text-ellipsis\"\r\n\t\t\t\t\t\t\t\tv-if=\"item.supplier\">{{ item.supplier.name }}</text>\r\n\t\t\t\t\t\t\t<!-- 堂食订单显示桌位信息 -->\r\n\t\t\t\t\t\t\t<text class=\"gray6 f24\" v-if=\"item.is_dine_in_master && item.table\">\r\n\t\t\t\t\t\t\t\t桌位：{{ item.table.table_no }}\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"state\">\r\n\t\t\t\t\t\t<text class=\"gray9 f24\">{{ item.state_text }}</text>\r\n\t\t\t\t\t\t<!-- 堂食订单显示子订单数量 -->\r\n\t\t\t\t\t\t<!-- <text class=\"gray6 f20\" v-if=\"item.is_dine_in_master\">\r\n\t\t\t\t\t\t\t({{ item.sub_orders_count }}次点餐)\r\n\t\t\t\t\t\t</text> -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!--多个商品显示-->\r\n\t\t\t\t<view class=\"product-list pr d-b-c\">\r\n\t\t\t\t\t<view class=\"o-a flex-1\">\r\n\t\t\t\t\t\t<view class=\"list d-s-c pr100\">\r\n\t\t\t\t\t\t\t<template v-for=\"(img, num) in item.product\">\r\n\t\t\t\t\t\t\t\t<view class=\"cover mr20\" :key=\"num\" v-if=\"num<=2\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"img.image ? img.image.file_path : ''\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t\t\t<view class=\"mt10 tc f24 text-ellipsis\">{{ img.product_name }}</view>\r\n\t\t\t\t\t\t\t\t\t<!-- 显示下单人信息（仅堂食订单） -->\r\n\t\t\t\t\t\t\t\t\t<view class=\"mt5 tc f20 gray9\" v-if=\"item.is_dine_in_master && img.order_by\">{{ img.order_by }}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view class=\"theme-price f30\">￥<text class=\"f36\">{{item.pay_price}}</text></view>\r\n\t\t\t\t\t\t<!-- 显示子订单统计信息（仅堂食订单） -->\r\n\t\t\t\t\t\t<!-- <view class=\"f20 gray6 tr\" v-if=\"item.is_dine_in_master\">\r\n\t\t\t\t\t\t\t共{{item.productNum}}件 ({{item.user_sub_orders_count || 0}}/{{item.sub_orders_count || 0}}次点餐)\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<!-- <view class=\"f20 gray6 tr\">共{{item.productNum}}件</view> -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"shop-name flex-1\">下单时间：{{ item.create_time }}</text>\r\n\t\t\t\t<!-- 堂食主订单不显示支付和取消按钮 -->\r\n\t\t\t\t<view class=\"order-bts\" v-if=\"item.order_status.value == 10 && !item.is_dine_in_master\">\r\n\t\t\t\t\t<!-- 未支付取消订单 -->\r\n\t\t\t\t\t<button class=\"default\" @click.stop=\"cancelOrder(item.order_id)\" type=\"default\"\r\n\t\t\t\t\t\tv-if=\"item.pay_status.value == 10&&item.order_source != 30\">取消订单</button>\r\n\t\t\t\t\t<!-- 订单付款 -->\r\n\t\t\t\t\t<template v-if=\"item.pay_status.value == 10&&item.order_source != 30\">\r\n\t\t\t\t\t\t<button class=\"theme-btn fb\" @click.stop=\"onPayOrder(item.order_id)\">立即支付</button>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t\t<template v-if=\"item.pay_status.value == 10&&item.order_source == 30&&item.supplier.pay_open==1\">\r\n\t\t\t\t\t\t<button class=\"theme-btn fb\" @click.stop=\"onPayOrder(item.order_id)\">立即支付</button>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"d-c-c d-c p30\" v-if=\"listData.length == 0 && !loading\">\r\n\t\t\t\t<image style=\"width: 268rpx;height: 263rpx;margin-top: 123rpx;\" src=\"/static/no-order.png\"\r\n\t\t\t\t\tmode=\"aspectFill\"></image>\r\n\t\t\t\t<view class=\"f26 gray9\">暂无记录</view>\r\n\t\t\t\t<view><button class=\"btn-normal theme-btn\" @click=\"gotoPage('/pages/index/index')\">立即点单</button></view>\r\n\t\t\t</view>\r\n\t\t\t<uni-load-more v-else :loadingType=\"loadingType\"></uni-load-more>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport uniLoadMore from '@/components/uni-load-more.vue';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tuniLoadMore\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t/*顶部刷新*/\r\n\t\t\t\ttopRefresh: false,\r\n\t\t\t\t/*数据*/\r\n\t\t\t\tlistData: [],\r\n\t\t\t\t/*数据类别*/\r\n\t\t\t\tdataType: 1,\r\n\t\t\t\t/*订单id*/\r\n\t\t\t\torder_id: 0,\r\n\t\t\t\t/*最后一页码数*/\r\n\t\t\t\tlast_page: 0,\r\n\t\t\t\t/*当前页面*/\r\n\t\t\t\tpage: 1,\r\n\t\t\t\t/*每页条数*/\r\n\t\t\t\tlist_rows: 10,\r\n\t\t\t\t/*有没有等多*/\r\n\t\t\t\tno_more: false,\r\n\t\t\t\t/*是否正在加载*/\r\n\t\t\t\tloading: true,\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t/*加载中状态*/\r\n\t\t\tloadingType() {\r\n\t\t\t\tif (this.loading) {\r\n\t\t\t\t\treturn 1;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (this.listData.length != 0 && this.no_more) {\r\n\t\t\t\t\t\treturn 2;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.initData();\r\n\t\t\tthis.getData();\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tlet self = this;\r\n\t\t\tif (self.page < self.last_page) {\r\n\t\t\t\tself.page++;\r\n\t\t\t\tself.getData();\r\n\t\t\t}\r\n\t\t\tself.no_more = true;\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinitData() {\r\n\t\t\t\tlet self = this;\r\n\t\t\t\tself.page = 1;\r\n\t\t\t\tself.listData = [];\r\n\t\t\t\tself.no_more = false;\r\n\t\t\t},\r\n\t\t\t/*状态切换*/\r\n\t\t\torderStateFunc(e) {\r\n\t\t\t\tlet self = this;\r\n\t\t\t\tif (self.loading) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (self.dataType != e) {\r\n\t\t\t\t\tself.page = 1;\r\n\t\t\t\t\tself.loading = true;\r\n\t\t\t\t\tself.listData = [];\r\n\t\t\t\t\tself.dataType = e;\r\n\t\t\t\t\tself.getData();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/*获取数据*/\r\n\t\t\tgetData() {\r\n\t\t\t\tif (!this.getUserId()) {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tlet self = this;\r\n\t\t\t\tself.loading = true;\r\n\t\t\t\tself._get(\r\n\t\t\t\t\t'user.order/lists', {\r\n\t\t\t\t\t\tdataType: self.dataType,\r\n\t\t\t\t\t\tpage: self.page,\r\n\t\t\t\t\t\tlist_rows: self.list_rows\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfunction(res) {\r\n\t\t\t\t\t\tself.loading = false;\r\n\t\t\t\t\t\tself.listData = self.listData.concat(res.data.list.data);\r\n\t\t\t\t\t\tself.last_page = res.data.list.last_page;\r\n\t\t\t\t\t\tif (res.data.list.last_page <= 1) {\r\n\t\t\t\t\t\t\tself.no_more = true;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tself.no_more = false;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t);\r\n\t\t\t},\r\n\t\t\t/*跳转页面*/\r\n\t\t\tgotoOrder(item) {\r\n\t\t\t\tif (item.is_dine_in_master) {\r\n\t\t\t\t\t// 堂食主订单跳转到主订单详情页面\r\n\t\t\t\t\tthis.gotoPage('/pages/order/dine-in-detail?master_order_id=' + item.master_order_id);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 普通订单跳转到订单详情页面\r\n\t\t\t\t\tthis.gotoPage('/pages/order/order-detail?order_id=' + item.order_id);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t/*支付方式选择*/\r\n\t\t\tonPayOrder(orderId) {\r\n\t\t\t\tlet self = this;\r\n\t\t\t\tlet pages = '/pages/order/cashier?order_type=10&order_id=' + orderId;\r\n\t\t\t\tself.gotoPage(pages, 'reLaunch');\r\n\t\t\t},\r\n\r\n\t\t\t/*确认收货*/\r\n\t\t\torderReceipt(order_id) {\r\n\t\t\t\tlet self = this;\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '您确定要收货吗?',\r\n\t\t\t\t\tsuccess: function(o) {\r\n\t\t\t\t\t\tif (o.confirm) {\r\n\t\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\t\ttitle: '正在处理',\r\n\t\t\t\t\t\t\t\tmask: true\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tself._post(\r\n\t\t\t\t\t\t\t\t'user.order/receipt', {\r\n\t\t\t\t\t\t\t\t\torder_id: order_id\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfunction(res) {\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tself.listData = [];\r\n\t\t\t\t\t\t\t\t\tself.getData();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '取消收货',\r\n\t\t\t\t\t\t\t\tduration: 1000,\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t/*取消订单*/\r\n\t\t\tcancelOrder(e) {\r\n\t\t\t\tlet self = this;\r\n\t\t\t\tlet order_id = e;\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '您确定要取消吗?',\r\n\t\t\t\t\tsuccess: function(o) {\r\n\t\t\t\t\t\tif (o.confirm) {\r\n\t\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\t\ttitle: '正在处理',\r\n\t\t\t\t\t\t\t\tmask: true\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tself._get(\r\n\t\t\t\t\t\t\t\t'user.order/cancel', {\r\n\t\t\t\t\t\t\t\t\torder_id: order_id\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfunction(res) {\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '操作成功',\r\n\t\t\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tself.listData = [];\r\n\t\t\t\t\t\t\t\t\tself.getData();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgohome() {\r\n\t\t\t\tthis.gotoPage('/pages/index/index');\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.icon-guanbi1.icon {\r\n\t\tposition: absolute;\r\n\t\tright: 26rpx;\r\n\t\ttop: 26rpx;\r\n\t\tz-index: 1;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.pop-code {\r\n\t\twidth: 280rpx;\r\n\t\theight: 280rpx;\r\n\t\tmargin-bottom: 18rpx;\r\n\t\tmargin-top: 28rpx;\r\n\t}\r\n\r\n\t.order-list .order-head .state-text {\r\n\t\tpadding: 10rpx 12rpx;\r\n\t\tmargin-right: 21rpx;\r\n\t\tborder-radius: 4rpx;\r\n\t\tbackground: #ffe7e4;\r\n\t\tfont-size: 22rpx;\r\n\t\t@include font_color('font_color');\r\n\t}\r\n\r\n\t.shop-name {\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.order-list {\r\n\t\tpadding: 28rpx 30rpx;\r\n\t}\r\n\r\n\t.order-list .item {\r\n\t\tmargin-bottom: 22rpx;\r\n\t\tpadding: 28rpx 22rpx;\r\n\t\tbackground: #ffffff;\r\n\t\topacity: 1;\r\n\t\tborder-radius: 20rpx;\r\n\t}\r\n\r\n\t.order-list .product-list,\r\n\t.order-list .one-product {\r\n\t\tpadding: 30rpx 0 27rpx 0;\r\n\t}\r\n\r\n\t.one-product .pro-info {\r\n\t\tpadding: 0 21rpx 0 37rpx;\r\n\t\tdisplay: -webkit-box;\r\n\t\twidth: 361rpx;\r\n\t\toverflow: hidden;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.order-list .cover {\r\n\t\theight: 100%;\r\n\t\ttext-align: start;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.order-list .cover image {\r\n\t\twidth: 148rpx;\r\n\t\theight: 148rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t.order-list .total-count {\r\n\t\tpadding-left: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\talign-items: flex-end;\r\n\t}\r\n\r\n\t.order-list .total-count .price {\r\n\t\tcolor: #ff5800;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: 800;\r\n\t}\r\n\r\n\t.total-count .count {\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: 400;\r\n\t\tline-height: 28rpx;\r\n\t\tcolor: #282828;\r\n\t\topacity: 0.5;\r\n\t}\r\n\r\n\t.order-list .product-list {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\t// width: 544rpx;\r\n\t\tmargin: 0rpx 0 20rpx 0;\r\n\t\tpadding: 0;\r\n\t}\r\n\r\n\t.order-list .product-list .list {\r\n\t\toverflow-x: auto;\r\n\t}\r\n\r\n\t.product-list .total-count {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground: rgba(255, 255, 255, 0.9);\r\n\t}\r\n\r\n\t.product-list .total-count .left-shadow {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tbottom: 0;\r\n\t\tleft: -24rpx;\r\n\t\twidth: 24rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.product-list .total-count .left-shadow::after {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tbottom: 0;\r\n\t\twidth: 24rpx;\r\n\t\tright: -12rpx;\r\n\t\tdisplay: block;\r\n\t\tcontent: '';\r\n\t\tbackground-image: radial-gradient(rgba(0, 0, 0, 0.2) 10%, rgba(0, 0, 0, 0.1) 40%, rgba(0, 0, 0, 0) 80%);\r\n\t}\r\n\r\n\t.order-list .order-bts {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: flex-end;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.order-list .order-bts button {\r\n\t\tmargin: 0;\r\n\t\tpadding: 0 27rpx;\r\n\t\theight: 60rpx;\r\n\t\tline-height: 60rpx;\r\n\t\tmargin-left: 20rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tborder: 1px solid;\r\n\t\tborder-radius: 80rpx;\r\n\t\tbackground: #ffffff;\r\n\t\twhite-space: nowrap;\r\n\t\tfont-family: PingFang SC;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.order-list .order-bts button::after {\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\t.order-list .order-bts button.default {\r\n\t\tborder: 1px solid #D2D2D2;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.order-list .order-bts button.btn-red {\r\n\t\t@include background_color('background_color');\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\t@include text_color('text_color');\r\n\t\tborder: 1px solid;\r\n\t\t@include border_color('border_color');\r\n\t}\r\n\r\n\t.buy-checkout {\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.buy-checkout .item {\r\n\t\tmin-height: 50rpx;\r\n\t\tline-height: 50rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.buy-checkout .iconfont.icon-weixin {\r\n\t\tcolor: #04be01;\r\n\t\tfont-size: 50rpx;\r\n\t}\r\n\r\n\t.buy-checkout .iconfont.icon-yue {\r\n\t\tcolor: #f0de7c;\r\n\t\tfont-size: 50rpx;\r\n\t}\r\n\r\n\t.buy-checkout .item.active .iconfont.icon-xuanze {\r\n\t\tcolor: #04be01;\r\n\t}\r\n\r\n\t.item-dianpu {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 30rpx;\r\n\t}\r\n\r\n\t.item-dianpu .icon-jiantou {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.item-d-l {\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.icon-dianpu1 {\r\n\t\tmargin-right: 20rpx;\r\n\t\tcolor: #333333;\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.bg-grayf2 {\r\n\t\tpadding: 8rpx;\r\n\t\tbackground-color: #f2f2f2;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t.tab-item-top {\r\n\t\twidth: 187rpx;\r\n\t\theight: 60rpx;\r\n\t\tcolor: #ffffff;\r\n\t\t@include font_color('font_color');\r\n\t\tfont-size: 32rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tborder: 2rpx solid;\r\n\t\t@include border_color('border_color');\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.tab-item {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tline-height: 42rpx;\r\n\t\tcolor: #282828;\r\n\t\topacity: 0.8;\r\n\t}\r\n\r\n\t.tab-item.active {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-family: PingFang SC;\r\n\t\tfont-weight: bold;\r\n\t\tline-height: 42rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.tab-item-top.left {\r\n\t\tborder-radius: 30rpx 0px 0px 30rpx;\r\n\t}\r\n\r\n\t.tab-item-top.right {\r\n\t\tborder-radius: 0px 30rpx 30rpx 0px;\r\n\t}\r\n\r\n\t.tab-item-top.active {\r\n\t\t@include background_color('background_color');\r\n\t\tcolor: #ffffff;\r\n\t\t@include font_color('font_color_inverse');\r\n\t\t//@include text_color('text_color');\r\n\t}\r\n\r\n\t.delivery_type {\r\n\t\twidth: 120rpx;\r\n\t\theight: 50rpx;\r\n\t\t@include font_color('font_color');\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: 1px solid;\r\n\t\t@include border_color('border_color');\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground-color: #ffffff;\r\n\t}\r\n\r\n\t.delivery_type.active {\r\n\t\t@include background_color('background_color');\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.head_top {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 30px;\r\n\t\tline-height: 30px;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.sup_img {\r\n\t\twidth: 44rpx;\r\n\t\theight: 44rpx;\r\n\t\tbackground: rgba(0, 0, 0, 0);\r\n\t\topacity: 1;\r\n\t\tborder-radius: 50%;\r\n\t\tmargin-right: 10rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.btn-normal {\r\n\t\twidth: 302rpx;\r\n\t\theight: 93rpx;\r\n\t\tborder-radius: 65rpx;\r\n\t\tmargin-top: 40rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 600;\r\n\t\t@include font_color('font_color_inverse');\r\n\t}\r\n\r\n\t.pop-bg {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100vh;\r\n\t\tbackground: rgba(0, 0, 0, 0.5);\r\n\t\tz-index: 1000;\r\n\r\n\t\t.pop-content {\r\n\t\t\tposition: fixed;\r\n\t\t\tz-index: 1001;\r\n\t\t\tbottom: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 719rpx;\r\n\t\t\tpadding: 40rpx 24rpx 0 23rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\ttransform: translate3d(0, 0, 0);\r\n\t\t\ttransition: transform 0.2s cubic-bezier(0, 0, 0.25, 1);\r\n\t\t\tbackground-color: #ffffff;\r\n\t\t\tborder-radius: 15rpx 15rpx 0rpx 0rpx;\r\n\r\n\t\t\t.icon.icon-guanbi {\r\n\t\t\t\tbackground: #dedede;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 26rpx;\r\n\t\t\t\ttop: 40rpx;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.close-img {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tposition: absolute;\r\n\t\tright: 32rpx;\r\n\t\ttop: 32rpx;\r\n\t}\r\n\r\n\t.pop-bg.close {\r\n\t\t// display: none;\r\n\t\theight: 0;\r\n\r\n\t\t.pop-content {\r\n\t\t\ttransform: translate3d(0, 2000rpx, 0);\r\n\t\t}\r\n\t}\r\n\r\n\t.cashier-item {\r\n\t\theight: 89rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tborder-bottom: 1px solid #eeeeee;\r\n\r\n\t\t.icon-box {\r\n\t\t\twidth: 38rpx;\r\n\t\t\theight: 38rpx;\r\n\t\t\tborder: 1px solid #dddddd;\r\n\t\t\tborder-radius: 50%;\r\n\r\n\t\t\t.icon-tijiaochenggong {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.icon-box.border {\r\n\t\t\tborder-radius: 50%;\r\n\t\t}\r\n\t}\r\n\r\n\t.cashier-item.active .icon-box {\r\n\t\tborder: 1px solid #72deed;\r\n\t\t@include border_color('border_color');\r\n\t\t@include background_color('background_color');\r\n\t}\r\n\r\n\t.pay-btn {\r\n\t\twidth: 750rpx;\r\n\t\theight: 96rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\t@include background_color('background_color');\r\n\t}\r\n\r\n\t.order-tips {\r\n\t\t@include font_color('full-reduction-font');\r\n\t\t@include background_color('full-reduction-bg');\r\n\t\twidth: 72rpx;\r\n\t\theight: 42rpx;\r\n\t\tborder-radius: 5rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 42rpx;\r\n\t\ttext-align: center;\r\n\t\tdisplay: inline-block;\r\n\t\tmargin-right: 7rpx;\r\n\t}\r\n</style>", "import MiniProgramPage from 'E:/code/diancan/jjjfood/jjj_food_chain_app/pages/order/myorder.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AA+EC,oBAAoB,MAAW;AAC/B,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,YAAY;AAAA;AAAA,MAEZ,UAAU,CAAE;AAAA;AAAA,MAEZ,UAAU;AAAA;AAAA,MAEV,UAAU;AAAA;AAAA,MAEV,WAAW;AAAA;AAAA,MAEX,MAAM;AAAA;AAAA,MAEN,WAAW;AAAA;AAAA,MAEX,SAAS;AAAA;AAAA,MAET,SAAS;AAAA;EAEV;AAAA,EACD,UAAU;AAAA;AAAA,IAET,cAAc;AACb,UAAI,KAAK,SAAS;AACjB,eAAO;AAAA,aACD;AACN,YAAI,KAAK,SAAS,UAAU,KAAK,KAAK,SAAS;AAC9C,iBAAO;AAAA,eACD;AACN,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,SAAQ;AACb,SAAK,QAAO;AAAA,EACZ;AAAA,EACD,gBAAgB;AACf,QAAI,OAAO;AACX,QAAI,KAAK,OAAO,KAAK,WAAW;AAC/B,WAAK;AACL,WAAK,QAAO;AAAA,IACb;AACA,SAAK,UAAU;AAAA,EACf;AAAA,EACD,SAAS;AAAA,IACR,WAAW;AACV,UAAI,OAAO;AACX,WAAK,OAAO;AACZ,WAAK,WAAW;AAChB,WAAK,UAAU;AAAA,IACf;AAAA;AAAA,IAED,eAAe,GAAG;AACjB,UAAI,OAAO;AACX,UAAI,KAAK,SAAS;AACjB;AAAA,MACD;AACA,UAAI,KAAK,YAAY,GAAG;AACvB,aAAK,OAAO;AACZ,aAAK,UAAU;AACf,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,QAAO;AAAA,MACb;AAAA,IACA;AAAA;AAAA,IAED,UAAU;AACT,UAAI,CAAC,KAAK,aAAa;AACtB,aAAK,UAAU;AACf;AAAA,MACD;AACA,UAAI,OAAO;AACX,WAAK,UAAU;AACf,WAAK;AAAA,QACJ;AAAA,QAAoB;AAAA,UACnB,UAAU,KAAK;AAAA,UACf,MAAM,KAAK;AAAA,UACX,WAAW,KAAK;AAAA,QAChB;AAAA,QACD,SAAS,KAAK;AACb,eAAK,UAAU;AACf,eAAK,WAAW,KAAK,SAAS,OAAO,IAAI,KAAK,KAAK,IAAI;AACvD,eAAK,YAAY,IAAI,KAAK,KAAK;AAC/B,cAAI,IAAI,KAAK,KAAK,aAAa,GAAG;AACjC,iBAAK,UAAU;AAAA,iBACT;AACN,iBAAK,UAAU;AAAA,UAChB;AAAA,QACD;AAAA;IAED;AAAA;AAAA,IAED,UAAU,MAAM;AACf,UAAI,KAAK,mBAAmB;AAE3B,aAAK,SAAS,iDAAiD,KAAK,eAAe;AAAA,aAC7E;AAEN,aAAK,SAAS,wCAAwC,KAAK,QAAQ;AAAA,MACpE;AAAA,IACA;AAAA;AAAA,IAGD,WAAW,SAAS;AACnB,UAAI,OAAO;AACX,UAAI,QAAQ,iDAAiD;AAC7D,WAAK,SAAS,OAAO,UAAU;AAAA,IAC/B;AAAA;AAAA,IAGD,aAAa,UAAU;AACtB,UAAI,OAAO;AACXA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,SAAS,GAAG;AACpB,cAAI,EAAE,SAAS;AACdA,0BAAAA,MAAI,YAAY;AAAA,cACf,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AACD,iBAAK;AAAA,cACJ;AAAA,cAAsB;AAAA,gBACrB;AAAA,cACA;AAAA,cACD,SAAS,KAAK;AACbA,8BAAG,MAAC,YAAW;AACfA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO,IAAI;AAAA,kBACX,UAAU;AAAA,kBACV,MAAM;AAAA,gBACP,CAAC;AACD,qBAAK,WAAW;AAChB,qBAAK,QAAO;AAAA,cACb;AAAA;iBAEK;AACNA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,UAAU;AAAA,cACV,MAAM;AAAA,YACP,CAAC;AAAA,UACF;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,GAAG;AACd,UAAI,OAAO;AACX,UAAI,WAAW;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,SAAS,GAAG;AACpB,cAAI,EAAE,SAAS;AACdA,0BAAAA,MAAI,YAAY;AAAA,cACf,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AACD,iBAAK;AAAA,cACJ;AAAA,cAAqB;AAAA,gBACpB;AAAA,cACA;AAAA,cACD,SAAS,KAAK;AACbA,8BAAG,MAAC,YAAW;AACfA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,UAAU;AAAA,kBACV,MAAM;AAAA,gBACP,CAAC;AACD,qBAAK,WAAW;AAChB,qBAAK,QAAO;AAAA,cACb;AAAA;UAEF;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA,IACD,SAAS;AACR,WAAK,SAAS,oBAAoB;AAAA,IACnC;AAAA,EACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7QF,GAAG,WAAW,eAAe;"}