{"version": 3, "file": "dine-in-detail.js", "sources": ["pages/order/dine-in-detail.vue", "D:/HBuilderX.3.4.18.20220630/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvb3JkZXIvZGluZS1pbi1kZXRhaWwudnVl"], "sourcesContent": ["<template>\n\t<view :data-theme=\"theme()\" :class=\"theme() || ''\">\n\t\t<view class=\"order-detail\" v-if=\"orderDetail\">\n\t\t\t<!-- 订单基本信息 -->\n\t\t\t<view class=\"order-info\">\n\t\t\t\t<view class=\"info-header\">\n\t\t\t\t\t<text class=\"order-title\">堂食订单详情</text>\n\t\t\t\t\t<view class=\"order-status\">\n\t\t\t\t\t\t<text class=\"status-text\">{{ orderDetail.order_status_text || '进行中' }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"info-content\">\n\t\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t\t<text class=\"label\">桌位号：</text>\n\t\t\t\t\t\t<text class=\"value\">{{ orderDetail.table ? orderDetail.table.table_no : '-' }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t\t<text class=\"label\">区域：</text>\n\t\t\t\t\t\t<text class=\"value\">{{ orderDetail.table ? orderDetail.table.area_name : '-' }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t\t<text class=\"label\">开台时间：</text>\n\t\t\t\t\t\t<text class=\"value\">{{ formatTime(orderDetail.open_time) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t\t<text class=\"label\">用餐时长：</text>\n\t\t\t\t\t\t<text class=\"value\">{{ getDiningDuration(orderDetail.open_time) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"info-row\">\n\t\t\t\t\t\t<text class=\"label\">我的消费：</text>\n\t\t\t\t\t\t<text class=\"value amount\">￥{{ userTotalAmount }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 我的点餐记录 -->\n\t\t\t<view class=\"sub-orders\">\n\t\t\t\t<view class=\"section-title\">我的点餐记录</view>\n\t\t\t\t<view class=\"sub-order-item\" v-for=\"(subOrder, index) in userSubOrders\" :key=\"subOrder.order_id\">\n\t\t\t\t\t<view class=\"sub-order-header\">\n\t\t\t\t\t\t<text class=\"order-time\">{{ subOrder.order_by || ('第' + (index + 1) + '次点餐') }} - {{ formatTime(subOrder.create_time) }}</text>\n\t\t\t\t\t\t<text class=\"order-amount\">￥{{ subOrder.pay_price }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 商品列表 -->\n\t\t\t\t\t<view class=\"product-list\">\n\t\t\t\t\t\t<view class=\"product-item\" v-for=\"product in subOrder.product\" :key=\"product.order_product_id\">\n\t\t\t\t\t\t\t<view class=\"product-image\">\n\t\t\t\t\t\t\t\t<image :src=\"product.image ? product.image.file_path : '/static/images/default-product.png'\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t\t\t\t<text class=\"product-name\">{{ product.product_name }}</text>\n\t\t\t\t\t\t\t\t<text class=\"product-attr\" v-if=\"product.product_attr\">{{ product.product_attr }}</text>\n\t\t\t\t\t\t\t\t<view class=\"product-price-num\">\n\t\t\t\t\t\t\t\t\t<text class=\"product-price\">￥{{ product.product_price }}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"product-num\">x{{ product.total_num }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"product-total\">\n\t\t\t\t\t\t\t\t<text class=\"total-price\">￥{{ product.total_price }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 加载状态 -->\n\t\t<view class=\"loading\" v-if=\"loading\">\n\t\t\t<text>加载中...</text>\n\t\t</view>\n\t\t\n\t\t<!-- 空状态 -->\n\t\t<view class=\"empty\" v-if=\"!loading && !orderDetail\">\n\t\t\t<text>订单不存在</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tloading: true,\n\t\t\t\tmasterOrderId: null,\n\t\t\t\torderDetail: null,\n\t\t\t\tuserSubOrders: [],\n\t\t\t\tuserTotalAmount: '0.00'\n\t\t\t}\n\t\t},\n\t\t\n\t\tonLoad(options) {\n\t\t\tthis.masterOrderId = options.master_order_id;\n\t\t\tif (this.masterOrderId) {\n\t\t\t\tthis.getOrderDetail();\n\t\t\t}\n\t\t},\n\t\t\n\t\tmethods: {\n\t\t\t// 获取订单详情\n\t\t\tgetOrderDetail() {\n\t\t\t\tlet self = this;\n\t\t\t\tself.loading = true;\n\t\t\t\t\n\t\t\t\tself._get('user.order/dineInDetail', {\n\t\t\t\t\tmaster_order_id: self.masterOrderId\n\t\t\t\t}, function(result) {\n\t\t\t\t\tself.loading = false;\n\t\t\t\t\tif (result.data) {\n\t\t\t\t\t\tself.orderDetail = result.data.master_order;\n\t\t\t\t\t\tself.userSubOrders = result.data.user_sub_orders || [];\n\t\t\t\t\t\tself.userTotalAmount = result.data.user_total_amount || '0.00';\n\t\t\t\t\t}\n\t\t\t\t}, function(err) {\n\t\t\t\t\tself.loading = false;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取订单详情失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 格式化时间\n\t\t\tformatTime(timestamp) {\n\t\t\t\tif (!timestamp) return '';\n\t\t\t\t\n\t\t\t\tlet date;\n\t\t\t\tif (typeof timestamp === 'string') {\n\t\t\t\t\tdate = new Date(timestamp);\n\t\t\t\t} else if (typeof timestamp === 'number') {\n\t\t\t\t\tif (timestamp.toString().length === 10) {\n\t\t\t\t\t\tdate = new Date(timestamp * 1000);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdate = new Date(timestamp);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn date.toLocaleString('zh-CN', {\n\t\t\t\t\tyear: 'numeric',\n\t\t\t\t\tmonth: '2-digit',\n\t\t\t\t\tday: '2-digit',\n\t\t\t\t\thour: '2-digit',\n\t\t\t\t\tminute: '2-digit'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 计算用餐时长\n\t\t\tgetDiningDuration(openTime) {\n\t\t\t\tif (!openTime) return '';\n\t\t\t\t\n\t\t\t\tconst now = Date.now();\n\t\t\t\tlet start;\n\t\t\t\t\n\t\t\t\tif (typeof openTime === 'string') {\n\t\t\t\t\tstart = new Date(openTime).getTime();\n\t\t\t\t} else if (typeof openTime === 'number') {\n\t\t\t\t\tif (openTime.toString().length === 10) {\n\t\t\t\t\t\tstart = openTime * 1000;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tstart = openTime;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (isNaN(start)) return '';\n\t\t\t\t\n\t\t\t\tconst duration = Math.floor((now - start) / 1000);\n\t\t\t\tconst hours = Math.floor(duration / 3600);\n\t\t\t\tconst minutes = Math.floor((duration % 3600) / 60);\n\t\t\t\treturn `${hours}小时${minutes}分钟`;\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.order-detail {\n\t\tpadding: 20rpx;\n\t\tbackground-color: #f5f5f5;\n\t\tmin-height: 100vh;\n\t}\n\t\n\t.order-info {\n\t\tbackground: white;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 30rpx;\n\t\tmargin-bottom: 20rpx;\n\t\t\n\t\t.info-header {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 30rpx;\n\t\t\t\n\t\t\t.order-title {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #333;\n\t\t\t}\n\t\t\t\n\t\t\t.order-status {\n\t\t\t\t.status-text {\n\t\t\t\t\tbackground: #e6f7ff;\n\t\t\t\t\tcolor: #1890ff;\n\t\t\t\t\tpadding: 8rpx 16rpx;\n\t\t\t\t\tborder-radius: 20rpx;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.info-content {\n\t\t\t.info-row {\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.label {\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.value {\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\n\t\t\t\t\t&.amount {\n\t\t\t\t\t\tcolor: #ff4d4f;\n\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.sub-orders {\n\t\t.section-title {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: bold;\n\t\t\tcolor: #333;\n\t\t\tmargin-bottom: 20rpx;\n\t\t}\n\t\t\n\t\t.sub-order-item {\n\t\t\tbackground: white;\n\t\t\tborder-radius: 12rpx;\n\t\t\tpadding: 30rpx;\n\t\t\tmargin-bottom: 20rpx;\n\t\t\t\n\t\t\t.sub-order-header {\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\tpadding-bottom: 20rpx;\n\t\t\t\tborder-bottom: 1px solid #f0f0f0;\n\t\t\t\t\n\t\t\t\t.order-time {\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.order-amount {\n\t\t\t\t\tcolor: #ff4d4f;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.product-list {\n\t\t\t\t.product-item {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\t\n\t\t\t\t\t&:last-child {\n\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.product-image {\n\t\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\t\theight: 120rpx;\n\t\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\tmargin-right: 20rpx;\n\t\t\t\t\t\t\n\t\t\t\t\t\timage {\n\t\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\t\theight: 100%;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.product-info {\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\t\n\t\t\t\t\t\t.product-name {\n\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.product-attr {\n\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.product-price-num {\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t.product-price {\n\t\t\t\t\t\t\t\tcolor: #ff4d4f;\n\t\t\t\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t.product-num {\n\t\t\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.product-total {\n\t\t\t\t\t\t.total-price {\n\t\t\t\t\t\t\tcolor: #ff4d4f;\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.loading, .empty {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\theight: 400rpx;\n\t\tcolor: #999;\n\t\tfont-size: 28rpx;\n\t}\n</style>\n", "import MiniProgramPage from 'E:/code/diancan/jjjfood/jjj_food_chain_app/pages/order/dine-in-detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAiFC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,MACT,eAAe;AAAA,MACf,aAAa;AAAA,MACb,eAAe,CAAE;AAAA,MACjB,iBAAiB;AAAA,IAClB;AAAA,EACA;AAAA,EAED,OAAO,SAAS;AACf,SAAK,gBAAgB,QAAQ;AAC7B,QAAI,KAAK,eAAe;AACvB,WAAK,eAAc;AAAA,IACpB;AAAA,EACA;AAAA,EAED,SAAS;AAAA;AAAA,IAER,iBAAiB;AAChB,UAAI,OAAO;AACX,WAAK,UAAU;AAEf,WAAK,KAAK,2BAA2B;AAAA,QACpC,iBAAiB,KAAK;AAAA,MACtB,GAAE,SAAS,QAAQ;AACnB,aAAK,UAAU;AACf,YAAI,OAAO,MAAM;AAChB,eAAK,cAAc,OAAO,KAAK;AAC/B,eAAK,gBAAgB,OAAO,KAAK,mBAAmB,CAAA;AACpD,eAAK,kBAAkB,OAAO,KAAK,qBAAqB;AAAA,QACzD;AAAA,MACA,GAAE,SAAS,KAAK;AAChB,aAAK,UAAU;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,WAAW;AACrB,UAAI,CAAC;AAAW,eAAO;AAEvB,UAAI;AACJ,UAAI,OAAO,cAAc,UAAU;AAClC,eAAO,IAAI,KAAK,SAAS;AAAA,MAC1B,WAAW,OAAO,cAAc,UAAU;AACzC,YAAI,UAAU,WAAW,WAAW,IAAI;AACvC,iBAAO,IAAI,KAAK,YAAY,GAAI;AAAA,eAC1B;AACN,iBAAO,IAAI,KAAK,SAAS;AAAA,QAC1B;AAAA,aACM;AACN,eAAO;AAAA,MACR;AAEA,UAAI,MAAM,KAAK,QAAO,CAAE,GAAG;AAC1B,eAAO;AAAA,MACR;AAEA,aAAO,KAAK,eAAe,SAAS;AAAA,QACnC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,MAAM;AAAA,QACN,QAAQ;AAAA,MACT,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB,UAAU;AAC3B,UAAI,CAAC;AAAU,eAAO;AAEtB,YAAM,MAAM,KAAK;AACjB,UAAI;AAEJ,UAAI,OAAO,aAAa,UAAU;AACjC,gBAAQ,IAAI,KAAK,QAAQ,EAAE,QAAO;AAAA,MACnC,WAAW,OAAO,aAAa,UAAU;AACxC,YAAI,SAAS,WAAW,WAAW,IAAI;AACtC,kBAAQ,WAAW;AAAA,eACb;AACN,kBAAQ;AAAA,QACT;AAAA,aACM;AACN,eAAO;AAAA,MACR;AAEA,UAAI,MAAM,KAAK;AAAG,eAAO;AAEzB,YAAM,WAAW,KAAK,OAAO,MAAM,SAAS,GAAI;AAChD,YAAM,QAAQ,KAAK,MAAM,WAAW,IAAI;AACxC,YAAM,UAAU,KAAK,MAAO,WAAW,OAAQ,EAAE;AACjD,aAAO,GAAG,KAAK,KAAK,OAAO;AAAA,IAC5B;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnLD,GAAG,WAAW,eAAe;"}