<?php

namespace app\api\model\order;

use app\api\service\order\paysuccess\type\MasterPaySuccessService;
use app\api\service\order\PaymentService;
use app\common\enum\order\OrderPayTypeEnum;
use app\common\enum\order\OrderSourceEnum;
use app\common\enum\order\OrderTypeEnum;
use app\common\enum\order\OrderStatusEnum;
use app\common\exception\BaseException;
use app\common\model\order\Order as OrderModel;
use app\api\service\order\checkpay\CheckPayFactory;
use app\common\service\order\OrderCompleteService;

/**
 * 普通订单模型
 */
class Order extends OrderModel
{
    /**
     * 隐藏字段
     * @var array
     */
    protected $hidden = [
        'update_time'
    ];

    /**
     * 订单支付事件
     */
    public function onPay($user)
    {
        // 判断订单状态
        $checkPay = CheckPayFactory::getFactory($this['order_source']);

        if (!$checkPay->checkOrderStatus($this)) {
            $this->error = $checkPay->getError();
            return false;
        }
        //判断是否为扫码点餐
        if ($this['order_source'] == OrderSourceEnum::SCAN) {
            $this->save(['user_id' => $user['user_id']]);
        }
        return true;
    }

    /**
     * 用户中心订单列表
     */
    public function getList($user_id, $params)
    {
        $model = $this;
        if (isset($params['shop_supplier_id']) && $params['shop_supplier_id']) {
            $model = $model->where('shop_supplier_id', '=', $params['shop_supplier_id']);
        }
        if (isset($params['order_type'])) {
            $model = $model->where('order_type', '=', $params['order_type']);
        }
        if (isset($params['delivery_type']) && $params['delivery_type']) {
            $model = $model->where('delivery_type', '=', $params['delivery_type']);
        }
        switch ($params['dataType']) {
            case '0'://全部订单
                break;
            case '1'://当前订单
                $model = $model->where('order_status', '=', 10);
                break;
            case '2'://历史订单
                $model = $model->where('order_status', '<>', 10);
                break;
        }
        // 排除堂食子订单，只显示主订单和其他类型订单
        $list = $model->with(['product.image', 'supplier'])
            ->where('user_id', '=', $user_id)
            ->where('is_delete', '=', 0)
            ->where(function($query) {
                // 不是子订单，或者不是堂食订单
                $query->where('is_sub_order', '=', 0)
                      ->whereOr(function($subQuery) {
                          $subQuery->where('is_sub_order', '=', 1)
                                   ->where('order_type', '<>', 1);
                      });
            })
            ->order(['create_time' => 'desc'])
            ->paginate($params);

        // 获取堂食主订单
        $dineInMasterOrders = $this->getDineInMasterOrders($user_id, $params);

        // 如果有堂食主订单，简单地添加到现有数据中
        if (!empty($dineInMasterOrders)) {
            // 获取现有数据
            $existingItems = $list->items();

            // 将堂食主订单添加到开头
            $allItems = array_merge($dineInMasterOrders, $existingItems);

            // 按时间排序
            usort($allItems, function($a, $b) {
                $timeA = is_string($a['create_time']) ? strtotime($a['create_time']) : $a['create_time'];
                $timeB = is_string($b['create_time']) ? strtotime($b['create_time']) : $b['create_time'];
                return $timeB - $timeA;
            });

            // 重新设置集合数据
            $list = $list->setCollection(collect($allItems));
        }
        foreach ($list as &$item) {
            $item['order_label'] = '';
            if ($item['order_type'] == 0 && $item['delivery_type']['value'] == 10) {
                $item['order_label'] = '外送';
            } elseif ($item['order_type'] == 0 && $item['delivery_type']['value'] == 20) {
                $item['order_label'] = '自提';
            } elseif ($item['order_type'] == 1 && $item['delivery_type']['value'] == 30) {
                $item['order_label'] = '打包';
            } elseif ($item['order_type'] == 1 && $item['delivery_type']['value'] == 40) {
                $item['order_label'] = '堂食';
            }
            $productNum = 0;
            //商品数量
            foreach ($item['product'] as $product) {
                $productNum += $product['total_num'];
            }
            $item['productNum'] = $productNum;
        }
        return $list;
    }

    /**
     * 获取用户的堂食主订单
     */
    private function getDineInMasterOrders($user_id, $params)
    {
        $masterOrderModel = new \app\common\model\order\MasterOrder();
        $model = $masterOrderModel;

        // 应用相同的筛选条件
        if (isset($params['shop_supplier_id']) && $params['shop_supplier_id']) {
            $model = $model->where('shop_supplier_id', '=', $params['shop_supplier_id']);
        }

        // 根据dataType筛选
        switch ($params['dataType']) {
            case '1'://当前订单
                $model = $model->where('order_status', '=', 10);
                break;
            case '2'://历史订单
                $model = $model->where('order_status', '<>', 10);
                break;
        }

        // 获取用户参与的堂食主订单
        $masterOrders = $model->with(['table', 'supplier'])
            ->whereExists(function($query) use ($user_id) {
                $query->table('jjjfood_order')
                      ->whereRaw('jjjfood_order.master_order_id = jjjfood_master_order.master_order_id')
                      ->where('user_id', '=', $user_id)
                      ->where('is_sub_order', '=', 1)
                      ->where('is_delete', '=', 0);
            })
            ->where('is_delete', '=', 0)
            ->order(['create_time' => 'desc'])
            ->limit(20) // 限制数量，避免性能问题
            ->select();

        $result = [];
        foreach ($masterOrders as $masterOrder) {
            // 获取该主订单下的所有子订单（包括代客点单）
            $allSubOrders = $this->with(['product.image', 'user'])
                ->where('master_order_id', '=', $masterOrder['master_order_id'])
                ->where('is_sub_order', '=', 1)
                ->where('is_delete', '=', 0)
                ->select();

            // 计算当前用户的消费总额
            $userTotalPrice = 0;
            foreach ($allSubOrders as $subOrder) {
                if ($subOrder['user_id'] == $user_id) {
                    $userTotalPrice += floatval($subOrder['pay_price'] ?? 0);
                }
            }

            if (!empty($allSubOrders)) {
                // 构造类似普通订单的数据结构
                $dineInOrder = [
                    'order_id' => $masterOrder['master_order_id'], // 使用主订单ID
                    'order_no' => $masterOrder['order_no'],
                    'master_order_id' => $masterOrder['master_order_id'],
                    'is_dine_in_master' => true, // 标记为堂食主订单
                    'order_type' => 1, // 堂食
                    'delivery_type' => ['value' => 40, 'text' => '堂食'],
                    'order_status' => $masterOrder['order_status'],
                    'pay_status' => $masterOrder['pay_status'],
                    'state_text' => $masterOrder['order_status_text'] ?? '进行中',
                    'create_time' => $masterOrder['create_time'],
                    'pay_price' => $userTotalPrice, // 当前用户的总消费
                    'supplier' => $masterOrder['supplier'],
                    'table' => $masterOrder['table'],
                    'order_label' => '堂食',
                    'product' => [], // 聚合所有商品
                    'productNum' => 0,
                    'sub_orders_count' => count($allSubOrders), // 所有子订单数量
                    'user_sub_orders_count' => 0, // 用户的子订单数量
                ];

                // 聚合所有商品信息，并标注下单人
                foreach ($allSubOrders as $subOrder) {
                    // 统计用户的子订单数量
                    if ($subOrder['user_id'] == $user_id) {
                        $dineInOrder['user_sub_orders_count']++;
                    }
                    // 标注下单人信息
                    $orderBy = '';
                    if ($subOrder['user_id'] == $user_id) {
                        $orderBy = '我的点餐';
                    } else {
                        if ($subOrder['user'] && $subOrder['user']['nickName']) {
                            $orderBy = $subOrder['user']['nickName'] . ' 的点餐';
                        } else {
                            $orderBy = '代客点餐';
                        }
                    }

                    if (isset($subOrder['product']) && is_array($subOrder['product'])) {
                        foreach ($subOrder['product'] as $product) {
                            // 确保商品数据结构正确
                            $productData = [
                                'product_id' => $product['product_id'] ?? 0,
                                'product_name' => $product['product_name'] ?? '',
                                'total_num' => $product['total_num'] ?? 1,
                                'image' => null,
                                'order_by' => $orderBy // 添加下单人信息
                            ];

                            // 处理图片数据
                            if (isset($product['image']) && $product['image']) {
                                $productData['image'] = $product['image'];
                            } elseif (isset($product['product_image']) && $product['product_image']) {
                                $productData['image'] = $product['product_image'];
                            }

                            $dineInOrder['product'][] = $productData;
                            $dineInOrder['productNum'] += intval($product['total_num'] ?? 1);
                        }
                    }
                }

                $result[] = $dineInOrder;
            }
        }

        return $result;
    }

    /**
     * 安全计算总价格
     */
    private function calculateTotalPrice($orders)
    {
        $total = 0;
        if (is_array($orders)) {
            foreach ($orders as $order) {
                $total += isset($order['pay_price']) ? floatval($order['pay_price']) : 0;
            }
        } elseif ($orders instanceof \think\Collection) {
            foreach ($orders as $order) {
                $total += isset($order['pay_price']) ? floatval($order['pay_price']) : 0;
            }
        }
        return $total;
    }

    /**
     * 取消订单
     */
    public function cancel($user)
    {
        if ($this['pay_status']['value'] == 20 || $this['order_status']['value'] != 10) {
            $this->error = '订单状态错误不允许取消!';
            return false;
        }
        return $this->save(['order_status' => OrderStatusEnum::CANCELLED]);
    }

    /**
     * 确认收货
     */
    public function receipt()
    {
        // 验证订单是否合法
        // 条件1: 订单必须已发货
        // 条件2: 订单必须未收货
        if ($this['delivery_status']['value'] != 20 || $this['receipt_status']['value'] != 10) {
            $this->error = '该订单不合法';
            return false;
        }
        return $this->transaction(function () {
            // 更新订单状态
            $status = $this->save([
                'receipt_status' => 20,
                'receipt_time' => time(),
                'order_status' => 30
            ]);
            // 执行订单完成后的操作
            $OrderCompleteService = new OrderCompleteService(OrderTypeEnum::MASTER);
            $OrderCompleteService->complete([$this], static::$app_id);
            return $status;
        });
    }

    /**
     * 订单详情
     */
    public static function getUserOrderDetail($order_id, $user_id)
    {
        $model = new static();
        $order = $model->where(['order_id' => $order_id])->with(['product' => ['image'], 'address', 'supplier', 'user', 'deliver'])->find();
        if (empty($order)) {
            throw new BaseException(['msg' => '订单不存在']);
        }
        return $order;
    }

    /**
     * 余额支付标记订单已支付
     */
    public function onPaymentByBalance($orderNo)
    {
        // 获取订单详情
        $PaySuccess = new MasterPaySuccessService($orderNo);
        // 发起余额支付
        $status = $PaySuccess->onPaySuccess(OrderPayTypeEnum::BALANCE);
        if (!$status) {
            $this->error = $PaySuccess->getError();
        }
        return $status;
    }

    /**
     * 构建微信支付请求
     */
    protected static function onPaymentByWechat($user, $order, $pay_source)
    {
        return PaymentService::wechat(
            $user,
            $order,
            OrderTypeEnum::MASTER,
            $pay_source
        );
    }

    /**
     * 待支付订单详情
     */
    public static function getPayDetail($orderNo)
    {
        $model = new static();
        return $model->where(['trade_no' => $orderNo, 'pay_status' => 10, 'is_delete' => 0])->with(['product', 'user', 'supplier'])->find();
    }

    /**
     * 构建支付请求的参数
     */
    public static function onOrderPayment($user, $order, $payType, $pay_source)
    {
        if ($payType == OrderPayTypeEnum::WECHAT) {
            return self::onPaymentByWechat($user, $order, $pay_source);
        }
        if ($pay_source == 'h5') {
            return [];
        }
        return [];
    }

    /**
     * 设置错误信息
     */
    protected function setError($error)
    {
        empty($this->error) && $this->error = $error;
    }

    /**
     * 是否存在错误
     */
    public function hasError()
    {
        return !empty($this->error);
    }

    /**
     * 主订单购买的数量
     * 未取消的订单
     */
    public static function getHasBuyOrderNum($user_id, $product_id)
    {
        $model = new static();
        return $model->alias('order')->where('order.user_id', '=', $user_id)
            ->join('order_product', 'order_product.order_id = order.order_id', 'left')
            ->where('order_product.product_id', '=', $product_id)
            ->where('order.order_source', '=', OrderSourceEnum::MASTER)
            ->where('order.order_status', '<>', 20)
            ->sum('total_num');
    }

    //查询桌号订单信息
    public static function getOrderInfo($table_id)
    {
        return (new static())->with('product')
            ->where('table_id', '=', $table_id)
            ->where('pay_status', '=', 10)
            ->where('order_status', '=', 10)
            ->where('is_delete', '=', 0)
            ->find();
    }

    /**
     * 创建新订单
     */
    public function OrderPay($params, $order, $user)
    {
        $payType = $params['payType'];
        $payment = '';
        $online_money = 0;
        $order->save(['balance' => 0, 'online_money' => 0, 'trade_no' => $this->tradeNo()]);
        if ($order['pay_price'] == 0) {
            $params['use_balance'] = 1;
        }
        // 余额支付标记订单已支付
        if ($params['use_balance'] == 1) {
            if ($user['balance'] >= $order['pay_price']) {
                $payType = 10;
                $order->save(['balance' => $order['pay_price']]);
                $this->onPaymentByBalance($order['trade_no']);
            } else {
                if ($payType <= 10) {
                    $this->error = '用户余额不足';
                    return false;
                }
                $online_money = round($order['pay_price'] - $user['balance'], 2);
                $order->save(['balance' => $user['balance'], 'online_money' => $online_money]);
            }
        } else {
            if ($payType <= 10) {
                $this->error = '请选择支付方式';
                return false;
            }
            $online_money = $order['pay_price'];
            $order->save(['online_money' => $order['pay_price']]);
        }
        $online_money > 0 && $payment = self::onOrderPayment($user, $order, $payType, $params['pay_source']);

        $result['order_id'] = $order['order_id'];
        $result['payType'] = $payType;
        $result['payment'] = $payment;
        return $result;
    }
}