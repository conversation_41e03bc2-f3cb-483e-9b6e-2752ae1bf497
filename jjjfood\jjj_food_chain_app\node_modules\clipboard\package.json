{"name": "clipboard", "version": "2.0.11", "description": "Modern copy to clipboard. No Flash. Just 2kb", "homepage": "https://clipboardjs.com", "repository": "zenorocha/clipboard.js", "license": "MIT", "main": "dist/clipboard.js", "types": "src/clipboard.d.ts", "keywords": ["clipboard", "copy", "cut"], "dependencies": {"good-listener": "^1.2.2", "select": "^1.1.2", "tiny-emitter": "^2.0.0"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/preset-env": "^7.12.11", "babel-loader": "^8.2.2", "chai": "^4.2.0", "cross-env": "^7.0.3", "eslint": "^7.20.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^7.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.3.1", "husky": "^5.0.9", "karma": "^6.0.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.1.0", "karma-mocha": "^2.0.1", "karma-sinon": "^1.0.4", "karma-webpack": "^5.0.0-alpha.5", "lint-staged": "^10.5.3", "mocha": "^8.2.1", "prettier": "2.2.1", "sinon": "^9.2.3", "tsd": "^0.7.2", "uglifyjs-webpack-plugin": "^2.2.0", "webpack": "^5.15.0", "webpack-cli": "^4.4.0"}, "scripts": {"test:types": "tsd", "build": "npm run build-debug && npm run build-min", "build-debug": "webpack", "build-min": "cross-env NODE_ENV=production webpack", "build-watch": "webpack --watch", "test": "karma start --single-run", "prepublish": "npm run build", "lint": "eslint --ext .js src/"}, "lint-staged": {"*.{js,css,md}": ["prettier --write", "eslint --fix"]}}