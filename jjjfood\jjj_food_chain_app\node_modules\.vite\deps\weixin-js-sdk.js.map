{"version": 3, "sources": ["../../weixin-js-sdk/index.js"], "sourcesContent": ["!(function(e, n) {\n  module.exports = n(e);\n})(window, function(o, e) {\n  if (!o.j<PERSON>eixin) {\n    var n,\n      c = {\n        config: \"preVerifyJSAPI\",\n        onMenuShareTimeline: \"menu:share:timeline\",\n        onMenuShareAppMessage: \"menu:share:appmessage\",\n        onMenuShareQQ: \"menu:share:qq\",\n        onMenuShareWeibo: \"menu:share:weiboApp\",\n        onMenuShareQZone: \"menu:share:QZone\",\n        previewImage: \"imagePreview\",\n        getLocation: \"geoLocation\",\n        openProductSpecificView: \"openProductViewWithPid\",\n        addCard: \"batchAddCard\",\n        openCard: \"batchViewCard\",\n        chooseWXPay: \"getBrandWCPayRequest\",\n        openEnterpriseRedPacket: \"getRecevieBizHongBaoRequest\",\n        startSearchBeacons: \"startMonitoringBeacons\",\n        stopSearchBeacons: \"stopMonitoringBeacons\",\n        onSearchBeacons: \"onBeaconsInRange\",\n        consumeAndShareCard: \"consumedShareCard\",\n        openAddress: \"editAddress\"\n      },\n      a = (function() {\n        var e = {};\n        for (var n in c) e[c[n]] = n;\n        return e;\n      })(),\n      i = o.document,\n      t = i.title,\n      r = navigator.userAgent.toLowerCase(),\n      s = navigator.platform.toLowerCase(),\n      d = !(!s.match(\"mac\") && !s.match(\"win\")),\n      u = -1 != r.indexOf(\"wxdebugger\"),\n      l = -1 != r.indexOf(\"micromessenger\"),\n      p = -1 != r.indexOf(\"android\"),\n      f = -1 != r.indexOf(\"iphone\") || -1 != r.indexOf(\"ipad\"),\n      m = (n =\n        r.match(/micromessenger\\/(\\d+\\.\\d+\\.\\d+)/) ||\n        r.match(/micromessenger\\/(\\d+\\.\\d+)/))\n        ? n[1]\n        : \"\",\n      g = {\n        initStartTime: L(),\n        initEndTime: 0,\n        preVerifyStartTime: 0,\n        preVerifyEndTime: 0\n      },\n      h = {\n        version: 1,\n        appId: \"\",\n        initTime: 0,\n        preVerifyTime: 0,\n        networkType: \"\",\n        isPreVerifyOk: 1,\n        systemType: f ? 1 : p ? 2 : -1,\n        clientVersion: m,\n        url: encodeURIComponent(location.href)\n      },\n      v = {},\n      S = { _completes: [] },\n      y = { state: 0, data: {} };\n    O(function() {\n      g.initEndTime = L();\n    });\n    var I = !1,\n      _ = [],\n      w = {\n        config: function(e) {\n          B(\"config\", (v = e));\n          var t = !1 !== v.check;\n          O(function() {\n            if (t)\n              M(\n                c.config,\n                {\n                  verifyJsApiList: C(v.jsApiList),\n                  verifyOpenTagList: C(v.openTagList)\n                },\n                (function() {\n                  (S._complete = function(e) {\n                    (g.preVerifyEndTime = L()), (y.state = 1), (y.data = e);\n                  }),\n                    (S.success = function(e) {\n                      h.isPreVerifyOk = 0;\n                    }),\n                    (S.fail = function(e) {\n                      S._fail ? S._fail(e) : (y.state = -1);\n                    });\n                  var t = S._completes;\n                  return (\n                    t.push(function() {\n                      !(function() {\n                        if (\n                          !(\n                            d ||\n                            u ||\n                            v.debug ||\n                            m < \"6.0.2\" ||\n                            h.systemType < 0\n                          )\n                        ) {\n                          var i = new Image();\n                          (h.appId = v.appId),\n                            (h.initTime = g.initEndTime - g.initStartTime),\n                            (h.preVerifyTime =\n                              g.preVerifyEndTime - g.preVerifyStartTime),\n                            w.getNetworkType({\n                              isInnerInvoke: !0,\n                              success: function(e) {\n                                h.networkType = e.networkType;\n                                var n =\n                                  \"https://open.weixin.qq.com/sdk/report?v=\" +\n                                  h.version +\n                                  \"&o=\" +\n                                  h.isPreVerifyOk +\n                                  \"&s=\" +\n                                  h.systemType +\n                                  \"&c=\" +\n                                  h.clientVersion +\n                                  \"&a=\" +\n                                  h.appId +\n                                  \"&n=\" +\n                                  h.networkType +\n                                  \"&i=\" +\n                                  h.initTime +\n                                  \"&p=\" +\n                                  h.preVerifyTime +\n                                  \"&u=\" +\n                                  h.url;\n                                i.src = n;\n                              }\n                            });\n                        }\n                      })();\n                    }),\n                    (S.complete = function(e) {\n                      for (var n = 0, i = t.length; n < i; ++n) t[n]();\n                      S._completes = [];\n                    }),\n                    S\n                  );\n                })()\n              ),\n                (g.preVerifyStartTime = L());\n            else {\n              y.state = 1;\n              for (var e = S._completes, n = 0, i = e.length; n < i; ++n)\n                e[n]();\n              S._completes = [];\n            }\n          }),\n            w.invoke ||\n              ((w.invoke = function(e, n, i) {\n                o.WeixinJSBridge && WeixinJSBridge.invoke(e, x(n), i);\n              }),\n              (w.on = function(e, n) {\n                o.WeixinJSBridge && WeixinJSBridge.on(e, n);\n              }));\n        },\n        ready: function(e) {\n          0 != y.state ? e() : (S._completes.push(e), !l && v.debug && e());\n        },\n        error: function(e) {\n          m < \"6.0.2\" || (-1 == y.state ? e(y.data) : (S._fail = e));\n        },\n        checkJsApi: function(e) {\n          M(\n            \"checkJsApi\",\n            { jsApiList: C(e.jsApiList) },\n            ((e._complete = function(e) {\n              if (p) {\n                var n = e.checkResult;\n                n && (e.checkResult = JSON.parse(n));\n              }\n              e = (function(e) {\n                var n = e.checkResult;\n                for (var i in n) {\n                  var t = a[i];\n                  t && ((n[t] = n[i]), delete n[i]);\n                }\n                return e;\n              })(e);\n            }),\n            e)\n          );\n        },\n        onMenuShareTimeline: function(e) {\n          P(\n            c.onMenuShareTimeline,\n            {\n              complete: function() {\n                M(\n                  \"shareTimeline\",\n                  {\n                    title: e.title || t,\n                    desc: e.title || t,\n                    img_url: e.imgUrl || \"\",\n                    link: e.link || location.href,\n                    type: e.type || \"link\",\n                    data_url: e.dataUrl || \"\"\n                  },\n                  e\n                );\n              }\n            },\n            e\n          );\n        },\n        onMenuShareAppMessage: function(n) {\n          P(\n            c.onMenuShareAppMessage,\n            {\n              complete: function(e) {\n                \"favorite\" === e.scene\n                  ? M(\"sendAppMessage\", {\n                      title: n.title || t,\n                      desc: n.desc || \"\",\n                      link: n.link || location.href,\n                      img_url: n.imgUrl || \"\",\n                      type: n.type || \"link\",\n                      data_url: n.dataUrl || \"\"\n                    })\n                  : M(\n                      \"sendAppMessage\",\n                      {\n                        title: n.title || t,\n                        desc: n.desc || \"\",\n                        link: n.link || location.href,\n                        img_url: n.imgUrl || \"\",\n                        type: n.type || \"link\",\n                        data_url: n.dataUrl || \"\"\n                      },\n                      n\n                    );\n              }\n            },\n            n\n          );\n        },\n        onMenuShareQQ: function(e) {\n          P(\n            c.onMenuShareQQ,\n            {\n              complete: function() {\n                M(\n                  \"shareQQ\",\n                  {\n                    title: e.title || t,\n                    desc: e.desc || \"\",\n                    img_url: e.imgUrl || \"\",\n                    link: e.link || location.href\n                  },\n                  e\n                );\n              }\n            },\n            e\n          );\n        },\n        onMenuShareWeibo: function(e) {\n          P(\n            c.onMenuShareWeibo,\n            {\n              complete: function() {\n                M(\n                  \"shareWeiboApp\",\n                  {\n                    title: e.title || t,\n                    desc: e.desc || \"\",\n                    img_url: e.imgUrl || \"\",\n                    link: e.link || location.href\n                  },\n                  e\n                );\n              }\n            },\n            e\n          );\n        },\n        onMenuShareQZone: function(e) {\n          P(\n            c.onMenuShareQZone,\n            {\n              complete: function() {\n                M(\n                  \"shareQZone\",\n                  {\n                    title: e.title || t,\n                    desc: e.desc || \"\",\n                    img_url: e.imgUrl || \"\",\n                    link: e.link || location.href\n                  },\n                  e\n                );\n              }\n            },\n            e\n          );\n        },\n        updateTimelineShareData: function(e) {\n          M(\n            \"updateTimelineShareData\",\n            { title: e.title, link: e.link, imgUrl: e.imgUrl },\n            e\n          );\n        },\n        updateAppMessageShareData: function(e) {\n          M(\n            \"updateAppMessageShareData\",\n            { title: e.title, desc: e.desc, link: e.link, imgUrl: e.imgUrl },\n            e\n          );\n        },\n        startRecord: function(e) {\n          M(\"startRecord\", {}, e);\n        },\n        stopRecord: function(e) {\n          M(\"stopRecord\", {}, e);\n        },\n        onVoiceRecordEnd: function(e) {\n          P(\"onVoiceRecordEnd\", e);\n        },\n        playVoice: function(e) {\n          M(\"playVoice\", { localId: e.localId }, e);\n        },\n        pauseVoice: function(e) {\n          M(\"pauseVoice\", { localId: e.localId }, e);\n        },\n        stopVoice: function(e) {\n          M(\"stopVoice\", { localId: e.localId }, e);\n        },\n        onVoicePlayEnd: function(e) {\n          P(\"onVoicePlayEnd\", e);\n        },\n        uploadVoice: function(e) {\n          M(\n            \"uploadVoice\",\n            {\n              localId: e.localId,\n              isShowProgressTips: 0 == e.isShowProgressTips ? 0 : 1\n            },\n            e\n          );\n        },\n        downloadVoice: function(e) {\n          M(\n            \"downloadVoice\",\n            {\n              serverId: e.serverId,\n              isShowProgressTips: 0 == e.isShowProgressTips ? 0 : 1\n            },\n            e\n          );\n        },\n        translateVoice: function(e) {\n          M(\n            \"translateVoice\",\n            {\n              localId: e.localId,\n              isShowProgressTips: 0 == e.isShowProgressTips ? 0 : 1\n            },\n            e\n          );\n        },\n        chooseImage: function(e) {\n          M(\n            \"chooseImage\",\n            {\n              scene: \"1|2\",\n              count: e.count || 9,\n              sizeType: e.sizeType || [\"original\", \"compressed\"],\n              sourceType: e.sourceType || [\"album\", \"camera\"]\n            },\n            ((e._complete = function(e) {\n              if (p) {\n                var n = e.localIds;\n                try {\n                  n && (e.localIds = JSON.parse(n));\n                } catch (e) {}\n              }\n            }),\n            e)\n          );\n        },\n        getLocation: function(e) {},\n        previewImage: function(e) {\n          M(c.previewImage, { current: e.current, urls: e.urls }, e);\n        },\n        uploadImage: function(e) {\n          M(\n            \"uploadImage\",\n            {\n              localId: e.localId,\n              isShowProgressTips: 0 == e.isShowProgressTips ? 0 : 1\n            },\n            e\n          );\n        },\n        downloadImage: function(e) {\n          M(\n            \"downloadImage\",\n            {\n              serverId: e.serverId,\n              isShowProgressTips: 0 == e.isShowProgressTips ? 0 : 1\n            },\n            e\n          );\n        },\n        getLocalImgData: function(e) {\n          !1 === I\n            ? ((I = !0),\n              M(\n                \"getLocalImgData\",\n                { localId: e.localId },\n                ((e._complete = function(e) {\n                  if (((I = !1), 0 < _.length)) {\n                    var n = _.shift();\n                    wx.getLocalImgData(n);\n                  }\n                }),\n                e)\n              ))\n            : _.push(e);\n        },\n        getNetworkType: function(e) {\n          M(\n            \"getNetworkType\",\n            {},\n            ((e._complete = function(e) {\n              e = (function(e) {\n                var n = e.errMsg;\n                e.errMsg = \"getNetworkType:ok\";\n                var i = e.subtype;\n                if ((delete e.subtype, i)) e.networkType = i;\n                else {\n                  var t = n.indexOf(\":\"),\n                    o = n.substring(t + 1);\n                  switch (o) {\n                    case \"wifi\":\n                    case \"edge\":\n                    case \"wwan\":\n                      e.networkType = o;\n                      break;\n                    default:\n                      e.errMsg = \"getNetworkType:fail\";\n                  }\n                }\n                return e;\n              })(e);\n            }),\n            e)\n          );\n        },\n        openLocation: function(e) {\n          M(\n            \"openLocation\",\n            {\n              latitude: e.latitude,\n              longitude: e.longitude,\n              name: e.name || \"\",\n              address: e.address || \"\",\n              scale: e.scale || 28,\n              infoUrl: e.infoUrl || \"\"\n            },\n            e\n          );\n        },\n        getLocation: function(e) {\n          M(\n            c.getLocation,\n            { type: (e = e || {}).type || \"wgs84\" },\n            ((e._complete = function(e) {\n              delete e.type;\n            }),\n            e)\n          );\n        },\n        hideOptionMenu: function(e) {\n          M(\"hideOptionMenu\", {}, e);\n        },\n        showOptionMenu: function(e) {\n          M(\"showOptionMenu\", {}, e);\n        },\n        closeWindow: function(e) {\n          M(\"closeWindow\", {}, (e = e || {}));\n        },\n        hideMenuItems: function(e) {\n          M(\"hideMenuItems\", { menuList: e.menuList }, e);\n        },\n        showMenuItems: function(e) {\n          M(\"showMenuItems\", { menuList: e.menuList }, e);\n        },\n        hideAllNonBaseMenuItem: function(e) {\n          M(\"hideAllNonBaseMenuItem\", {}, e);\n        },\n        showAllNonBaseMenuItem: function(e) {\n          M(\"showAllNonBaseMenuItem\", {}, e);\n        },\n        scanQRCode: function(e) {\n          M(\n            \"scanQRCode\",\n            {\n              needResult: (e = e || {}).needResult || 0,\n              scanType: e.scanType || [\"qrCode\", \"barCode\"]\n            },\n            ((e._complete = function(e) {\n              if (f) {\n                var n = e.resultStr;\n                if (n) {\n                  var i = JSON.parse(n);\n                  e.resultStr = i && i.scan_code && i.scan_code.scan_result;\n                }\n              }\n            }),\n            e)\n          );\n        },\n        openAddress: function(e) {\n          M(\n            c.openAddress,\n            {},\n            ((e._complete = function(e) {\n              e = (function(e) {\n                return (\n                  (e.postalCode = e.addressPostalCode),\n                  delete e.addressPostalCode,\n                  (e.provinceName = e.proviceFirstStageName),\n                  delete e.proviceFirstStageName,\n                  (e.cityName = e.addressCitySecondStageName),\n                  delete e.addressCitySecondStageName,\n                  (e.countryName = e.addressCountiesThirdStageName),\n                  delete e.addressCountiesThirdStageName,\n                  (e.detailInfo = e.addressDetailInfo),\n                  delete e.addressDetailInfo,\n                  e\n                );\n              })(e);\n            }),\n            e)\n          );\n        },\n        openProductSpecificView: function(e) {\n          M(\n            c.openProductSpecificView,\n            {\n              pid: e.productId,\n              view_type: e.viewType || 0,\n              ext_info: e.extInfo\n            },\n            e\n          );\n        },\n        addCard: function(e) {\n          for (var n = e.cardList, i = [], t = 0, o = n.length; t < o; ++t) {\n            var r = n[t],\n              a = { card_id: r.cardId, card_ext: r.cardExt };\n            i.push(a);\n          }\n          M(\n            c.addCard,\n            { card_list: i },\n            ((e._complete = function(e) {\n              var n = e.card_list;\n              if (n) {\n                for (var i = 0, t = (n = JSON.parse(n)).length; i < t; ++i) {\n                  var o = n[i];\n                  (o.cardId = o.card_id),\n                    (o.cardExt = o.card_ext),\n                    (o.isSuccess = !!o.is_succ),\n                    delete o.card_id,\n                    delete o.card_ext,\n                    delete o.is_succ;\n                }\n                (e.cardList = n), delete e.card_list;\n              }\n            }),\n            e)\n          );\n        },\n        chooseCard: function(e) {\n          M(\n            \"chooseCard\",\n            {\n              app_id: v.appId,\n              location_id: e.shopId || \"\",\n              sign_type: e.signType || \"SHA1\",\n              card_id: e.cardId || \"\",\n              card_type: e.cardType || \"\",\n              card_sign: e.cardSign,\n              time_stamp: e.timestamp + \"\",\n              nonce_str: e.nonceStr\n            },\n            ((e._complete = function(e) {\n              (e.cardList = e.choose_card_info), delete e.choose_card_info;\n            }),\n            e)\n          );\n        },\n        openCard: function(e) {\n          for (var n = e.cardList, i = [], t = 0, o = n.length; t < o; ++t) {\n            var r = n[t],\n              a = { card_id: r.cardId, code: r.code };\n            i.push(a);\n          }\n          M(c.openCard, { card_list: i }, e);\n        },\n        consumeAndShareCard: function(e) {\n          M(\n            c.consumeAndShareCard,\n            { consumedCardId: e.cardId, consumedCode: e.code },\n            e\n          );\n        },\n        chooseWXPay: function(e) {\n          M(c.chooseWXPay, V(e), e);\n        },\n        openEnterpriseRedPacket: function(e) {\n          M(c.openEnterpriseRedPacket, V(e), e);\n        },\n        startSearchBeacons: function(e) {\n          M(c.startSearchBeacons, { ticket: e.ticket }, e);\n        },\n        stopSearchBeacons: function(e) {\n          M(c.stopSearchBeacons, {}, e);\n        },\n        onSearchBeacons: function(e) {\n          P(c.onSearchBeacons, e);\n        },\n        openEnterpriseChat: function(e) {\n          M(\n            \"openEnterpriseChat\",\n            { useridlist: e.userIds, chatname: e.groupName },\n            e\n          );\n        },\n        launchMiniProgram: function(e) {\n          M(\n            \"launchMiniProgram\",\n            {\n              targetAppId: e.targetAppId,\n              path: (function(e) {\n                if (\"string\" == typeof e && 0 < e.length) {\n                  var n = e.split(\"?\")[0],\n                    i = e.split(\"?\")[1];\n                  return (n += \".html\"), void 0 !== i ? n + \"?\" + i : n;\n                }\n              })(e.path),\n              envVersion: e.envVersion\n            },\n            e\n          );\n        },\n        openBusinessView: function(e) {\n          M(\n            \"openBusinessView\",\n            {\n              businessType: e.businessType,\n              queryString: e.queryString || \"\",\n              envVersion: e.envVersion\n            },\n            ((e._complete = function(n) {\n              if (p) {\n                var e = n.extraData;\n                if (e)\n                  try {\n                    n.extraData = JSON.parse(e);\n                  } catch (e) {\n                    n.extraData = {};\n                  }\n              }\n            }),\n            e)\n          );\n        },\n        miniProgram: {\n          navigateBack: function(e) {\n            (e = e || {}),\n              O(function() {\n                M(\n                  \"invokeMiniProgramAPI\",\n                  { name: \"navigateBack\", arg: { delta: e.delta || 1 } },\n                  e\n                );\n              });\n          },\n          navigateTo: function(e) {\n            O(function() {\n              M(\n                \"invokeMiniProgramAPI\",\n                { name: \"navigateTo\", arg: { url: e.url } },\n                e\n              );\n            });\n          },\n          redirectTo: function(e) {\n            O(function() {\n              M(\n                \"invokeMiniProgramAPI\",\n                { name: \"redirectTo\", arg: { url: e.url } },\n                e\n              );\n            });\n          },\n          switchTab: function(e) {\n            O(function() {\n              M(\n                \"invokeMiniProgramAPI\",\n                { name: \"switchTab\", arg: { url: e.url } },\n                e\n              );\n            });\n          },\n          reLaunch: function(e) {\n            O(function() {\n              M(\n                \"invokeMiniProgramAPI\",\n                { name: \"reLaunch\", arg: { url: e.url } },\n                e\n              );\n            });\n          },\n          postMessage: function(e) {\n            O(function() {\n              M(\n                \"invokeMiniProgramAPI\",\n                { name: \"postMessage\", arg: e.data || {} },\n                e\n              );\n            });\n          },\n          getEnv: function(e) {\n            O(function() {\n              e({ miniprogram: \"miniprogram\" === o.__wxjs_environment });\n            });\n          }\n        }\n      },\n      T = 1,\n      k = {};\n    return (\n      i.addEventListener(\n        \"error\",\n        function(e) {\n          if (!p) {\n            var n = e.target,\n              i = n.tagName,\n              t = n.src;\n            if (\"IMG\" == i || \"VIDEO\" == i || \"AUDIO\" == i || \"SOURCE\" == i)\n              if (-1 != t.indexOf(\"wxlocalresource://\")) {\n                e.preventDefault(), e.stopPropagation();\n                var o = n[\"wx-id\"];\n                if ((o || ((o = T++), (n[\"wx-id\"] = o)), k[o])) return;\n                (k[o] = !0),\n                  wx.ready(function() {\n                    wx.getLocalImgData({\n                      localId: t,\n                      success: function(e) {\n                        n.src = e.localData;\n                      }\n                    });\n                  });\n              }\n          }\n        },\n        !0\n      ),\n      i.addEventListener(\n        \"load\",\n        function(e) {\n          if (!p) {\n            var n = e.target,\n              i = n.tagName;\n            n.src;\n            if (\"IMG\" == i || \"VIDEO\" == i || \"AUDIO\" == i || \"SOURCE\" == i) {\n              var t = n[\"wx-id\"];\n              t && (k[t] = !1);\n            }\n          }\n        },\n        !0\n      ),\n      e && (o.wx = o.jWeixin = w),\n      w\n    );\n  }\n  function M(n, e, i) {\n    o.WeixinJSBridge\n      ? WeixinJSBridge.invoke(n, x(e), function(e) {\n          A(n, e, i);\n        })\n      : B(n, i);\n  }\n  function P(n, i, t) {\n    o.WeixinJSBridge\n      ? WeixinJSBridge.on(n, function(e) {\n          t && t.trigger && t.trigger(e), A(n, e, i);\n        })\n      : B(n, t || i);\n  }\n  function x(e) {\n    return (\n      ((e = e || {}).appId = v.appId),\n      (e.verifyAppId = v.appId),\n      (e.verifySignType = \"sha1\"),\n      (e.verifyTimestamp = v.timestamp + \"\"),\n      (e.verifyNonceStr = v.nonceStr),\n      (e.verifySignature = v.signature),\n      e\n    );\n  }\n  function V(e) {\n    return {\n      timeStamp: e.timestamp + \"\",\n      nonceStr: e.nonceStr,\n      package: e.package,\n      paySign: e.paySign,\n      signType: e.signType || \"SHA1\"\n    };\n  }\n  function A(e, n, i) {\n    (\"openEnterpriseChat\" != e && \"openBusinessView\" !== e) ||\n      (n.errCode = n.err_code),\n      delete n.err_code,\n      delete n.err_desc,\n      delete n.err_detail;\n    var t = n.errMsg;\n    t ||\n      ((t = n.err_msg),\n      delete n.err_msg,\n      (t = (function(e, n) {\n        var i = e,\n          t = a[i];\n        t && (i = t);\n        var o = \"ok\";\n        if (n) {\n          var r = n.indexOf(\":\");\n          \"confirm\" == (o = n.substring(r + 1)) && (o = \"ok\"),\n            \"failed\" == o && (o = \"fail\"),\n            -1 != o.indexOf(\"failed_\") && (o = o.substring(7)),\n            -1 != o.indexOf(\"fail_\") && (o = o.substring(5)),\n            (\"access denied\" !=\n              (o = (o = o.replace(/_/g, \" \")).toLowerCase()) &&\n              \"no permission to execute\" != o) ||\n              (o = \"permission denied\"),\n            \"config\" == i && \"function not exist\" == o && (o = \"ok\"),\n            \"\" == o && (o = \"fail\");\n        }\n        return (n = i + \":\" + o);\n      })(e, t)),\n      (n.errMsg = t)),\n      (i = i || {})._complete && (i._complete(n), delete i._complete),\n      (t = n.errMsg || \"\"),\n      v.debug && !i.isInnerInvoke && alert(JSON.stringify(n));\n    var o = t.indexOf(\":\");\n    switch (t.substring(o + 1)) {\n      case \"ok\":\n        i.success && i.success(n);\n        break;\n      case \"cancel\":\n        i.cancel && i.cancel(n);\n        break;\n      default:\n        i.fail && i.fail(n);\n    }\n    i.complete && i.complete(n);\n  }\n  function C(e) {\n    if (e) {\n      for (var n = 0, i = e.length; n < i; ++n) {\n        var t = e[n],\n          o = c[t];\n        o && (e[n] = o);\n      }\n      return e;\n    }\n  }\n  function B(e, n) {\n    if (!(!v.debug || (n && n.isInnerInvoke))) {\n      var i = a[e];\n      i && (e = i),\n        n && n._complete && delete n._complete,\n        console.log('\"' + e + '\",', n || \"\");\n    }\n  }\n  function L() {\n    return new Date().getTime();\n  }\n  function O(e) {\n    l &&\n      (o.WeixinJSBridge\n        ? e()\n        : i.addEventListener &&\n          i.addEventListener(\"WeixinJSBridgeReady\", e, !1));\n  }\n});\n"], "mappings": ";;;;;;AAAA;AAAA;AAAA,KAAE,SAAS,GAAG,GAAG;AACf,aAAO,UAAU,EAAE,CAAC;AAAA,IACtB,EAAG,QAAQ,SAAS,GAAG,GAAG;AACxB,UAAI,CAAC,EAAE,SAAS;AACd,YAAI,GACF,IAAI;AAAA,UACF,QAAQ;AAAA,UACR,qBAAqB;AAAA,UACrB,uBAAuB;AAAA,UACvB,eAAe;AAAA,UACf,kBAAkB;AAAA,UAClB,kBAAkB;AAAA,UAClB,cAAc;AAAA,UACd,aAAa;AAAA,UACb,yBAAyB;AAAA,UACzB,SAAS;AAAA,UACT,UAAU;AAAA,UACV,aAAa;AAAA,UACb,yBAAyB;AAAA,UACzB,oBAAoB;AAAA,UACpB,mBAAmB;AAAA,UACnB,iBAAiB;AAAA,UACjB,qBAAqB;AAAA,UACrB,aAAa;AAAA,QACf,GACA,IAAK,WAAW;AACd,cAAIA,KAAI,CAAC;AACT,mBAASC,MAAK;AAAG,YAAAD,GAAE,EAAEC,EAAC,CAAC,IAAIA;AAC3B,iBAAOD;AAAA,QACT,EAAG,GACH,IAAI,EAAE,UACN,IAAI,EAAE,OACN,IAAI,UAAU,UAAU,YAAY,GACpC,IAAI,UAAU,SAAS,YAAY,GACnC,IAAI,EAAE,CAAC,EAAE,MAAM,KAAK,KAAK,CAAC,EAAE,MAAM,KAAK,IACvC,IAAI,MAAM,EAAE,QAAQ,YAAY,GAChC,IAAI,MAAM,EAAE,QAAQ,gBAAgB,GACpC,IAAI,MAAM,EAAE,QAAQ,SAAS,GAC7B,IAAI,MAAM,EAAE,QAAQ,QAAQ,KAAK,MAAM,EAAE,QAAQ,MAAM,GACvD,KAAK,IACH,EAAE,MAAM,iCAAiC,KACzC,EAAE,MAAM,4BAA4B,KAClC,EAAE,CAAC,IACH,IACJ,IAAI;AAAA,UACF,eAAe,EAAE;AAAA,UACjB,aAAa;AAAA,UACb,oBAAoB;AAAA,UACpB,kBAAkB;AAAA,QACpB,GACA,IAAI;AAAA,UACF,SAAS;AAAA,UACT,OAAO;AAAA,UACP,UAAU;AAAA,UACV,eAAe;AAAA,UACf,aAAa;AAAA,UACb,eAAe;AAAA,UACf,YAAY,IAAI,IAAI,IAAI,IAAI;AAAA,UAC5B,eAAe;AAAA,UACf,KAAK,mBAAmB,SAAS,IAAI;AAAA,QACvC,GACA,IAAI,CAAC,GACL,IAAI,EAAE,YAAY,CAAC,EAAE,GACrB,IAAI,EAAE,OAAO,GAAG,MAAM,CAAC,EAAE;AAC3B,UAAE,WAAW;AACX,YAAE,cAAc,EAAE;AAAA,QACpB,CAAC;AACD,YAAI,IAAI,OACN,IAAI,CAAC,GACL,IAAI;AAAA,UACF,QAAQ,SAASA,IAAG;AAClB,cAAE,UAAW,IAAIA,EAAE;AACnB,gBAAIE,KAAI,UAAO,EAAE;AACjB,cAAE,WAAW;AACX,kBAAIA;AACF;AAAA,kBACE,EAAE;AAAA,kBACF;AAAA,oBACE,iBAAiB,EAAE,EAAE,SAAS;AAAA,oBAC9B,mBAAmB,EAAE,EAAE,WAAW;AAAA,kBACpC;AAAA,kBACC,WAAW;AACV,oBAAC,EAAE,YAAY,SAASF,IAAG;AACzB,sBAAC,EAAE,mBAAmB,EAAE,GAAK,EAAE,QAAQ,GAAK,EAAE,OAAOA;AAAA,oBACvD,GACG,EAAE,UAAU,SAASA,IAAG;AACvB,wBAAE,gBAAgB;AAAA,oBACpB,GACC,EAAE,OAAO,SAASA,IAAG;AACpB,wBAAE,QAAQ,EAAE,MAAMA,EAAC,IAAK,EAAE,QAAQ;AAAA,oBACpC;AACF,wBAAIE,KAAI,EAAE;AACV,2BACEA,GAAE,KAAK,WAAW;AAChB,uBAAE,WAAW;AACX,4BACE,EACE,KACA,KACA,EAAE,SACF,IAAI,WACJ,EAAE,aAAa,IAEjB;AACA,8BAAIC,KAAI,IAAI,MAAM;AAClB,0BAAC,EAAE,QAAQ,EAAE,OACV,EAAE,WAAW,EAAE,cAAc,EAAE,eAC/B,EAAE,gBACD,EAAE,mBAAmB,EAAE,oBACzB,EAAE,eAAe;AAAA,4BACf,eAAe;AAAA,4BACf,SAAS,SAASH,IAAG;AACnB,gCAAE,cAAcA,GAAE;AAClB,kCAAIC,KACF,6CACA,EAAE,UACF,QACA,EAAE,gBACF,QACA,EAAE,aACF,QACA,EAAE,gBACF,QACA,EAAE,QACF,QACA,EAAE,cACF,QACA,EAAE,WACF,QACA,EAAE,gBACF,QACA,EAAE;AACJ,8BAAAE,GAAE,MAAMF;AAAA,4BACV;AAAA,0BACF,CAAC;AAAA,wBACL;AAAA,sBACF,EAAG;AAAA,oBACL,CAAC,GACA,EAAE,WAAW,SAASD,IAAG;AACxB,+BAASC,KAAI,GAAGE,KAAID,GAAE,QAAQD,KAAIE,IAAG,EAAEF;AAAG,wBAAAC,GAAED,EAAC,EAAE;AAC/C,wBAAE,aAAa,CAAC;AAAA,oBAClB,GACA;AAAA,kBAEJ,EAAG;AAAA,gBACL,GACG,EAAE,qBAAqB,EAAE;AAAA,mBACzB;AACH,kBAAE,QAAQ;AACV,yBAASD,KAAI,EAAE,YAAYC,KAAI,GAAGE,KAAIH,GAAE,QAAQC,KAAIE,IAAG,EAAEF;AACvD,kBAAAD,GAAEC,EAAC,EAAE;AACP,kBAAE,aAAa,CAAC;AAAA,cAClB;AAAA,YACF,CAAC,GACC,EAAE,WACE,EAAE,SAAS,SAASD,IAAGC,IAAGE,IAAG;AAC7B,gBAAE,kBAAkB,eAAe,OAAOH,IAAG,EAAEC,EAAC,GAAGE,EAAC;AAAA,YACtD,GACC,EAAE,KAAK,SAASH,IAAGC,IAAG;AACrB,gBAAE,kBAAkB,eAAe,GAAGD,IAAGC,EAAC;AAAA,YAC5C;AAAA,UACN;AAAA,UACA,OAAO,SAASD,IAAG;AACjB,iBAAK,EAAE,QAAQA,GAAE,KAAK,EAAE,WAAW,KAAKA,EAAC,GAAG,CAAC,KAAK,EAAE,SAASA,GAAE;AAAA,UACjE;AAAA,UACA,OAAO,SAASA,IAAG;AACjB,gBAAI,YAAY,MAAM,EAAE,QAAQA,GAAE,EAAE,IAAI,IAAK,EAAE,QAAQA;AAAA,UACzD;AAAA,UACA,YAAY,SAASA,IAAG;AACtB;AAAA,cACE;AAAA,cACA,EAAE,WAAW,EAAEA,GAAE,SAAS,EAAE;AAAA,eAC1BA,GAAE,YAAY,SAASA,IAAG;AAC1B,oBAAI,GAAG;AACL,sBAAIC,KAAID,GAAE;AACV,kBAAAC,OAAMD,GAAE,cAAc,KAAK,MAAMC,EAAC;AAAA,gBACpC;AACA,gBAAAD,KAAK,SAASA,IAAG;AACf,sBAAIC,KAAID,GAAE;AACV,2BAASG,MAAKF,IAAG;AACf,wBAAIC,KAAI,EAAEC,EAAC;AACX,oBAAAD,OAAOD,GAAEC,EAAC,IAAID,GAAEE,EAAC,GAAI,OAAOF,GAAEE,EAAC;AAAA,kBACjC;AACA,yBAAOH;AAAA,gBACT,EAAGA,EAAC;AAAA,cACN,GACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,qBAAqB,SAASA,IAAG;AAC/B;AAAA,cACE,EAAE;AAAA,cACF;AAAA,gBACE,UAAU,WAAW;AACnB;AAAA,oBACE;AAAA,oBACA;AAAA,sBACE,OAAOA,GAAE,SAAS;AAAA,sBAClB,MAAMA,GAAE,SAAS;AAAA,sBACjB,SAASA,GAAE,UAAU;AAAA,sBACrB,MAAMA,GAAE,QAAQ,SAAS;AAAA,sBACzB,MAAMA,GAAE,QAAQ;AAAA,sBAChB,UAAUA,GAAE,WAAW;AAAA,oBACzB;AAAA,oBACAA;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,uBAAuB,SAASC,IAAG;AACjC;AAAA,cACE,EAAE;AAAA,cACF;AAAA,gBACE,UAAU,SAASD,IAAG;AACpB,iCAAeA,GAAE,QACb,EAAE,kBAAkB;AAAA,oBAClB,OAAOC,GAAE,SAAS;AAAA,oBAClB,MAAMA,GAAE,QAAQ;AAAA,oBAChB,MAAMA,GAAE,QAAQ,SAAS;AAAA,oBACzB,SAASA,GAAE,UAAU;AAAA,oBACrB,MAAMA,GAAE,QAAQ;AAAA,oBAChB,UAAUA,GAAE,WAAW;AAAA,kBACzB,CAAC,IACD;AAAA,oBACE;AAAA,oBACA;AAAA,sBACE,OAAOA,GAAE,SAAS;AAAA,sBAClB,MAAMA,GAAE,QAAQ;AAAA,sBAChB,MAAMA,GAAE,QAAQ,SAAS;AAAA,sBACzB,SAASA,GAAE,UAAU;AAAA,sBACrB,MAAMA,GAAE,QAAQ;AAAA,sBAChB,UAAUA,GAAE,WAAW;AAAA,oBACzB;AAAA,oBACAA;AAAA,kBACF;AAAA,gBACN;AAAA,cACF;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,eAAe,SAASD,IAAG;AACzB;AAAA,cACE,EAAE;AAAA,cACF;AAAA,gBACE,UAAU,WAAW;AACnB;AAAA,oBACE;AAAA,oBACA;AAAA,sBACE,OAAOA,GAAE,SAAS;AAAA,sBAClB,MAAMA,GAAE,QAAQ;AAAA,sBAChB,SAASA,GAAE,UAAU;AAAA,sBACrB,MAAMA,GAAE,QAAQ,SAAS;AAAA,oBAC3B;AAAA,oBACAA;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,kBAAkB,SAASA,IAAG;AAC5B;AAAA,cACE,EAAE;AAAA,cACF;AAAA,gBACE,UAAU,WAAW;AACnB;AAAA,oBACE;AAAA,oBACA;AAAA,sBACE,OAAOA,GAAE,SAAS;AAAA,sBAClB,MAAMA,GAAE,QAAQ;AAAA,sBAChB,SAASA,GAAE,UAAU;AAAA,sBACrB,MAAMA,GAAE,QAAQ,SAAS;AAAA,oBAC3B;AAAA,oBACAA;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,kBAAkB,SAASA,IAAG;AAC5B;AAAA,cACE,EAAE;AAAA,cACF;AAAA,gBACE,UAAU,WAAW;AACnB;AAAA,oBACE;AAAA,oBACA;AAAA,sBACE,OAAOA,GAAE,SAAS;AAAA,sBAClB,MAAMA,GAAE,QAAQ;AAAA,sBAChB,SAASA,GAAE,UAAU;AAAA,sBACrB,MAAMA,GAAE,QAAQ,SAAS;AAAA,oBAC3B;AAAA,oBACAA;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,yBAAyB,SAASA,IAAG;AACnC;AAAA,cACE;AAAA,cACA,EAAE,OAAOA,GAAE,OAAO,MAAMA,GAAE,MAAM,QAAQA,GAAE,OAAO;AAAA,cACjDA;AAAA,YACF;AAAA,UACF;AAAA,UACA,2BAA2B,SAASA,IAAG;AACrC;AAAA,cACE;AAAA,cACA,EAAE,OAAOA,GAAE,OAAO,MAAMA,GAAE,MAAM,MAAMA,GAAE,MAAM,QAAQA,GAAE,OAAO;AAAA,cAC/DA;AAAA,YACF;AAAA,UACF;AAAA,UACA,aAAa,SAASA,IAAG;AACvB,cAAE,eAAe,CAAC,GAAGA,EAAC;AAAA,UACxB;AAAA,UACA,YAAY,SAASA,IAAG;AACtB,cAAE,cAAc,CAAC,GAAGA,EAAC;AAAA,UACvB;AAAA,UACA,kBAAkB,SAASA,IAAG;AAC5B,cAAE,oBAAoBA,EAAC;AAAA,UACzB;AAAA,UACA,WAAW,SAASA,IAAG;AACrB,cAAE,aAAa,EAAE,SAASA,GAAE,QAAQ,GAAGA,EAAC;AAAA,UAC1C;AAAA,UACA,YAAY,SAASA,IAAG;AACtB,cAAE,cAAc,EAAE,SAASA,GAAE,QAAQ,GAAGA,EAAC;AAAA,UAC3C;AAAA,UACA,WAAW,SAASA,IAAG;AACrB,cAAE,aAAa,EAAE,SAASA,GAAE,QAAQ,GAAGA,EAAC;AAAA,UAC1C;AAAA,UACA,gBAAgB,SAASA,IAAG;AAC1B,cAAE,kBAAkBA,EAAC;AAAA,UACvB;AAAA,UACA,aAAa,SAASA,IAAG;AACvB;AAAA,cACE;AAAA,cACA;AAAA,gBACE,SAASA,GAAE;AAAA,gBACX,oBAAoB,KAAKA,GAAE,qBAAqB,IAAI;AAAA,cACtD;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,eAAe,SAASA,IAAG;AACzB;AAAA,cACE;AAAA,cACA;AAAA,gBACE,UAAUA,GAAE;AAAA,gBACZ,oBAAoB,KAAKA,GAAE,qBAAqB,IAAI;AAAA,cACtD;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,gBAAgB,SAASA,IAAG;AAC1B;AAAA,cACE;AAAA,cACA;AAAA,gBACE,SAASA,GAAE;AAAA,gBACX,oBAAoB,KAAKA,GAAE,qBAAqB,IAAI;AAAA,cACtD;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,aAAa,SAASA,IAAG;AACvB;AAAA,cACE;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,OAAOA,GAAE,SAAS;AAAA,gBAClB,UAAUA,GAAE,YAAY,CAAC,YAAY,YAAY;AAAA,gBACjD,YAAYA,GAAE,cAAc,CAAC,SAAS,QAAQ;AAAA,cAChD;AAAA,eACEA,GAAE,YAAY,SAASA,IAAG;AAC1B,oBAAI,GAAG;AACL,sBAAIC,KAAID,GAAE;AACV,sBAAI;AACF,oBAAAC,OAAMD,GAAE,WAAW,KAAK,MAAMC,EAAC;AAAA,kBACjC,SAASD,IAAP;AAAA,kBAAW;AAAA,gBACf;AAAA,cACF,GACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,aAAa,SAASA,IAAG;AAAA,UAAC;AAAA,UAC1B,cAAc,SAASA,IAAG;AACxB,cAAE,EAAE,cAAc,EAAE,SAASA,GAAE,SAAS,MAAMA,GAAE,KAAK,GAAGA,EAAC;AAAA,UAC3D;AAAA,UACA,aAAa,SAASA,IAAG;AACvB;AAAA,cACE;AAAA,cACA;AAAA,gBACE,SAASA,GAAE;AAAA,gBACX,oBAAoB,KAAKA,GAAE,qBAAqB,IAAI;AAAA,cACtD;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,eAAe,SAASA,IAAG;AACzB;AAAA,cACE;AAAA,cACA;AAAA,gBACE,UAAUA,GAAE;AAAA,gBACZ,oBAAoB,KAAKA,GAAE,qBAAqB,IAAI;AAAA,cACtD;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,iBAAiB,SAASA,IAAG;AAC3B,sBAAO,KACD,IAAI,MACN;AAAA,cACE;AAAA,cACA,EAAE,SAASA,GAAE,QAAQ;AAAA,eACnBA,GAAE,YAAY,SAASA,IAAG;AAC1B,oBAAM,IAAI,OAAK,IAAI,EAAE,QAAS;AAC5B,sBAAIC,KAAI,EAAE,MAAM;AAChB,qBAAG,gBAAgBA,EAAC;AAAA,gBACtB;AAAA,cACF,GACAD;AAAA,YACF,KACA,EAAE,KAAKA,EAAC;AAAA,UACd;AAAA,UACA,gBAAgB,SAASA,IAAG;AAC1B;AAAA,cACE;AAAA,cACA,CAAC;AAAA,eACCA,GAAE,YAAY,SAASA,IAAG;AAC1B,gBAAAA,KAAK,SAASA,IAAG;AACf,sBAAIC,KAAID,GAAE;AACV,kBAAAA,GAAE,SAAS;AACX,sBAAIG,KAAIH,GAAE;AACV,sBAAK,OAAOA,GAAE,SAASG;AAAI,oBAAAH,GAAE,cAAcG;AAAA,uBACtC;AACH,wBAAID,KAAID,GAAE,QAAQ,GAAG,GACnBG,KAAIH,GAAE,UAAUC,KAAI,CAAC;AACvB,4BAAQE,IAAG;AAAA,sBACT,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AACH,wBAAAJ,GAAE,cAAcI;AAChB;AAAA,sBACF;AACE,wBAAAJ,GAAE,SAAS;AAAA,oBACf;AAAA,kBACF;AACA,yBAAOA;AAAA,gBACT,EAAGA,EAAC;AAAA,cACN,GACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,cAAc,SAASA,IAAG;AACxB;AAAA,cACE;AAAA,cACA;AAAA,gBACE,UAAUA,GAAE;AAAA,gBACZ,WAAWA,GAAE;AAAA,gBACb,MAAMA,GAAE,QAAQ;AAAA,gBAChB,SAASA,GAAE,WAAW;AAAA,gBACtB,OAAOA,GAAE,SAAS;AAAA,gBAClB,SAASA,GAAE,WAAW;AAAA,cACxB;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,aAAa,SAASA,IAAG;AACvB;AAAA,cACE,EAAE;AAAA,cACF,EAAE,OAAOA,KAAIA,MAAK,CAAC,GAAG,QAAQ,QAAQ;AAAA,eACpCA,GAAE,YAAY,SAASA,IAAG;AAC1B,uBAAOA,GAAE;AAAA,cACX,GACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,gBAAgB,SAASA,IAAG;AAC1B,cAAE,kBAAkB,CAAC,GAAGA,EAAC;AAAA,UAC3B;AAAA,UACA,gBAAgB,SAASA,IAAG;AAC1B,cAAE,kBAAkB,CAAC,GAAGA,EAAC;AAAA,UAC3B;AAAA,UACA,aAAa,SAASA,IAAG;AACvB,cAAE,eAAe,CAAC,GAAIA,KAAIA,MAAK,CAAC,CAAE;AAAA,UACpC;AAAA,UACA,eAAe,SAASA,IAAG;AACzB,cAAE,iBAAiB,EAAE,UAAUA,GAAE,SAAS,GAAGA,EAAC;AAAA,UAChD;AAAA,UACA,eAAe,SAASA,IAAG;AACzB,cAAE,iBAAiB,EAAE,UAAUA,GAAE,SAAS,GAAGA,EAAC;AAAA,UAChD;AAAA,UACA,wBAAwB,SAASA,IAAG;AAClC,cAAE,0BAA0B,CAAC,GAAGA,EAAC;AAAA,UACnC;AAAA,UACA,wBAAwB,SAASA,IAAG;AAClC,cAAE,0BAA0B,CAAC,GAAGA,EAAC;AAAA,UACnC;AAAA,UACA,YAAY,SAASA,IAAG;AACtB;AAAA,cACE;AAAA,cACA;AAAA,gBACE,aAAaA,KAAIA,MAAK,CAAC,GAAG,cAAc;AAAA,gBACxC,UAAUA,GAAE,YAAY,CAAC,UAAU,SAAS;AAAA,cAC9C;AAAA,eACEA,GAAE,YAAY,SAASA,IAAG;AAC1B,oBAAI,GAAG;AACL,sBAAIC,KAAID,GAAE;AACV,sBAAIC,IAAG;AACL,wBAAIE,KAAI,KAAK,MAAMF,EAAC;AACpB,oBAAAD,GAAE,YAAYG,MAAKA,GAAE,aAAaA,GAAE,UAAU;AAAA,kBAChD;AAAA,gBACF;AAAA,cACF,GACAH;AAAA,YACF;AAAA,UACF;AAAA,UACA,aAAa,SAASA,IAAG;AACvB;AAAA,cACE,EAAE;AAAA,cACF,CAAC;AAAA,eACCA,GAAE,YAAY,SAASA,IAAG;AAC1B,gBAAAA,KAAK,SAASA,IAAG;AACf,yBACGA,GAAE,aAAaA,GAAE,mBAClB,OAAOA,GAAE,mBACRA,GAAE,eAAeA,GAAE,uBACpB,OAAOA,GAAE,uBACRA,GAAE,WAAWA,GAAE,4BAChB,OAAOA,GAAE,4BACRA,GAAE,cAAcA,GAAE,+BACnB,OAAOA,GAAE,+BACRA,GAAE,aAAaA,GAAE,mBAClB,OAAOA,GAAE,mBACTA;AAAA,gBAEJ,EAAGA,EAAC;AAAA,cACN,GACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,yBAAyB,SAASA,IAAG;AACnC;AAAA,cACE,EAAE;AAAA,cACF;AAAA,gBACE,KAAKA,GAAE;AAAA,gBACP,WAAWA,GAAE,YAAY;AAAA,gBACzB,UAAUA,GAAE;AAAA,cACd;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,SAAS,SAASA,IAAG;AACnB,qBAASC,KAAID,GAAE,UAAUG,KAAI,CAAC,GAAGD,KAAI,GAAGE,KAAIH,GAAE,QAAQC,KAAIE,IAAG,EAAEF,IAAG;AAChE,kBAAIG,KAAIJ,GAAEC,EAAC,GACTI,KAAI,EAAE,SAASD,GAAE,QAAQ,UAAUA,GAAE,QAAQ;AAC/C,cAAAF,GAAE,KAAKG,EAAC;AAAA,YACV;AACA;AAAA,cACE,EAAE;AAAA,cACF,EAAE,WAAWH,GAAE;AAAA,eACbH,GAAE,YAAY,SAASA,IAAG;AAC1B,oBAAIC,KAAID,GAAE;AACV,oBAAIC,IAAG;AACL,2BAASE,KAAI,GAAGD,MAAKD,KAAI,KAAK,MAAMA,EAAC,GAAG,QAAQE,KAAID,IAAG,EAAEC,IAAG;AAC1D,wBAAIC,KAAIH,GAAEE,EAAC;AACX,oBAACC,GAAE,SAASA,GAAE,SACXA,GAAE,UAAUA,GAAE,UACdA,GAAE,YAAY,CAAC,CAACA,GAAE,SACnB,OAAOA,GAAE,SACT,OAAOA,GAAE,UACT,OAAOA,GAAE;AAAA,kBACb;AACA,kBAACJ,GAAE,WAAWC,IAAI,OAAOD,GAAE;AAAA,gBAC7B;AAAA,cACF,GACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,YAAY,SAASA,IAAG;AACtB;AAAA,cACE;AAAA,cACA;AAAA,gBACE,QAAQ,EAAE;AAAA,gBACV,aAAaA,GAAE,UAAU;AAAA,gBACzB,WAAWA,GAAE,YAAY;AAAA,gBACzB,SAASA,GAAE,UAAU;AAAA,gBACrB,WAAWA,GAAE,YAAY;AAAA,gBACzB,WAAWA,GAAE;AAAA,gBACb,YAAYA,GAAE,YAAY;AAAA,gBAC1B,WAAWA,GAAE;AAAA,cACf;AAAA,eACEA,GAAE,YAAY,SAASA,IAAG;AAC1B,gBAACA,GAAE,WAAWA,GAAE,kBAAmB,OAAOA,GAAE;AAAA,cAC9C,GACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,UAAU,SAASA,IAAG;AACpB,qBAASC,KAAID,GAAE,UAAUG,KAAI,CAAC,GAAGD,KAAI,GAAGE,KAAIH,GAAE,QAAQC,KAAIE,IAAG,EAAEF,IAAG;AAChE,kBAAIG,KAAIJ,GAAEC,EAAC,GACTI,KAAI,EAAE,SAASD,GAAE,QAAQ,MAAMA,GAAE,KAAK;AACxC,cAAAF,GAAE,KAAKG,EAAC;AAAA,YACV;AACA,cAAE,EAAE,UAAU,EAAE,WAAWH,GAAE,GAAGH,EAAC;AAAA,UACnC;AAAA,UACA,qBAAqB,SAASA,IAAG;AAC/B;AAAA,cACE,EAAE;AAAA,cACF,EAAE,gBAAgBA,GAAE,QAAQ,cAAcA,GAAE,KAAK;AAAA,cACjDA;AAAA,YACF;AAAA,UACF;AAAA,UACA,aAAa,SAASA,IAAG;AACvB,cAAE,EAAE,aAAa,EAAEA,EAAC,GAAGA,EAAC;AAAA,UAC1B;AAAA,UACA,yBAAyB,SAASA,IAAG;AACnC,cAAE,EAAE,yBAAyB,EAAEA,EAAC,GAAGA,EAAC;AAAA,UACtC;AAAA,UACA,oBAAoB,SAASA,IAAG;AAC9B,cAAE,EAAE,oBAAoB,EAAE,QAAQA,GAAE,OAAO,GAAGA,EAAC;AAAA,UACjD;AAAA,UACA,mBAAmB,SAASA,IAAG;AAC7B,cAAE,EAAE,mBAAmB,CAAC,GAAGA,EAAC;AAAA,UAC9B;AAAA,UACA,iBAAiB,SAASA,IAAG;AAC3B,cAAE,EAAE,iBAAiBA,EAAC;AAAA,UACxB;AAAA,UACA,oBAAoB,SAASA,IAAG;AAC9B;AAAA,cACE;AAAA,cACA,EAAE,YAAYA,GAAE,SAAS,UAAUA,GAAE,UAAU;AAAA,cAC/CA;AAAA,YACF;AAAA,UACF;AAAA,UACA,mBAAmB,SAASA,IAAG;AAC7B;AAAA,cACE;AAAA,cACA;AAAA,gBACE,aAAaA,GAAE;AAAA,gBACf,MAAO,SAASA,IAAG;AACjB,sBAAI,YAAY,OAAOA,MAAK,IAAIA,GAAE,QAAQ;AACxC,wBAAIC,KAAID,GAAE,MAAM,GAAG,EAAE,CAAC,GACpBG,KAAIH,GAAE,MAAM,GAAG,EAAE,CAAC;AACpB,2BAAQC,MAAK,SAAU,WAAWE,KAAIF,KAAI,MAAME,KAAIF;AAAA,kBACtD;AAAA,gBACF,EAAGD,GAAE,IAAI;AAAA,gBACT,YAAYA,GAAE;AAAA,cAChB;AAAA,cACAA;AAAA,YACF;AAAA,UACF;AAAA,UACA,kBAAkB,SAASA,IAAG;AAC5B;AAAA,cACE;AAAA,cACA;AAAA,gBACE,cAAcA,GAAE;AAAA,gBAChB,aAAaA,GAAE,eAAe;AAAA,gBAC9B,YAAYA,GAAE;AAAA,cAChB;AAAA,eACEA,GAAE,YAAY,SAASC,IAAG;AAC1B,oBAAI,GAAG;AACL,sBAAID,KAAIC,GAAE;AACV,sBAAID;AACF,wBAAI;AACF,sBAAAC,GAAE,YAAY,KAAK,MAAMD,EAAC;AAAA,oBAC5B,SAASA,IAAP;AACA,sBAAAC,GAAE,YAAY,CAAC;AAAA,oBACjB;AAAA,gBACJ;AAAA,cACF,GACAD;AAAA,YACF;AAAA,UACF;AAAA,UACA,aAAa;AAAA,YACX,cAAc,SAASA,IAAG;AACxB,cAACA,KAAIA,MAAK,CAAC,GACT,EAAE,WAAW;AACX;AAAA,kBACE;AAAA,kBACA,EAAE,MAAM,gBAAgB,KAAK,EAAE,OAAOA,GAAE,SAAS,EAAE,EAAE;AAAA,kBACrDA;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACL;AAAA,YACA,YAAY,SAASA,IAAG;AACtB,gBAAE,WAAW;AACX;AAAA,kBACE;AAAA,kBACA,EAAE,MAAM,cAAc,KAAK,EAAE,KAAKA,GAAE,IAAI,EAAE;AAAA,kBAC1CA;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,YACA,YAAY,SAASA,IAAG;AACtB,gBAAE,WAAW;AACX;AAAA,kBACE;AAAA,kBACA,EAAE,MAAM,cAAc,KAAK,EAAE,KAAKA,GAAE,IAAI,EAAE;AAAA,kBAC1CA;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,YACA,WAAW,SAASA,IAAG;AACrB,gBAAE,WAAW;AACX;AAAA,kBACE;AAAA,kBACA,EAAE,MAAM,aAAa,KAAK,EAAE,KAAKA,GAAE,IAAI,EAAE;AAAA,kBACzCA;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,YACA,UAAU,SAASA,IAAG;AACpB,gBAAE,WAAW;AACX;AAAA,kBACE;AAAA,kBACA,EAAE,MAAM,YAAY,KAAK,EAAE,KAAKA,GAAE,IAAI,EAAE;AAAA,kBACxCA;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,YACA,aAAa,SAASA,IAAG;AACvB,gBAAE,WAAW;AACX;AAAA,kBACE;AAAA,kBACA,EAAE,MAAM,eAAe,KAAKA,GAAE,QAAQ,CAAC,EAAE;AAAA,kBACzCA;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,YACA,QAAQ,SAASA,IAAG;AAClB,gBAAE,WAAW;AACX,gBAAAA,GAAE,EAAE,aAAa,kBAAkB,EAAE,mBAAmB,CAAC;AAAA,cAC3D,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF,GACA,IAAI,GACJ,IAAI,CAAC;AACP,eACE,EAAE;AAAA,UACA;AAAA,UACA,SAASA,IAAG;AACV,gBAAI,CAAC,GAAG;AACN,kBAAIC,KAAID,GAAE,QACRG,KAAIF,GAAE,SACNC,KAAID,GAAE;AACR,kBAAI,SAASE,MAAK,WAAWA,MAAK,WAAWA,MAAK,YAAYA;AAC5D,oBAAI,MAAMD,GAAE,QAAQ,oBAAoB,GAAG;AACzC,kBAAAF,GAAE,eAAe,GAAGA,GAAE,gBAAgB;AACtC,sBAAII,KAAIH,GAAE,OAAO;AACjB,sBAAKG,OAAOA,KAAI,KAAOH,GAAE,OAAO,IAAIG,KAAK,EAAEA,EAAC;AAAI;AAChD,kBAAC,EAAEA,EAAC,IAAI,MACN,GAAG,MAAM,WAAW;AAClB,uBAAG,gBAAgB;AAAA,sBACjB,SAASF;AAAA,sBACT,SAAS,SAASF,IAAG;AACnB,wBAAAC,GAAE,MAAMD,GAAE;AAAA,sBACZ;AAAA,oBACF,CAAC;AAAA,kBACH,CAAC;AAAA,gBACL;AAAA;AAAA,YACJ;AAAA,UACF;AAAA,UACA;AAAA,QACF,GACA,EAAE;AAAA,UACA;AAAA,UACA,SAASA,IAAG;AACV,gBAAI,CAAC,GAAG;AACN,kBAAIC,KAAID,GAAE,QACRG,KAAIF,GAAE;AACR,cAAAA,GAAE;AACF,kBAAI,SAASE,MAAK,WAAWA,MAAK,WAAWA,MAAK,YAAYA,IAAG;AAC/D,oBAAID,KAAID,GAAE,OAAO;AACjB,gBAAAC,OAAM,EAAEA,EAAC,IAAI;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,QACF,GACA,MAAM,EAAE,KAAK,EAAE,UAAU,IACzB;AAAA,MAEJ;AACA,eAAS,EAAED,IAAGD,IAAGG,IAAG;AAClB,UAAE,iBACE,eAAe,OAAOF,IAAG,EAAED,EAAC,GAAG,SAASA,IAAG;AACzC,YAAEC,IAAGD,IAAGG,EAAC;AAAA,QACX,CAAC,IACD,EAAEF,IAAGE,EAAC;AAAA,MACZ;AACA,eAAS,EAAEF,IAAGE,IAAGD,IAAG;AAClB,UAAE,iBACE,eAAe,GAAGD,IAAG,SAASD,IAAG;AAC/B,UAAAE,MAAKA,GAAE,WAAWA,GAAE,QAAQF,EAAC,GAAG,EAAEC,IAAGD,IAAGG,EAAC;AAAA,QAC3C,CAAC,IACD,EAAEF,IAAGC,MAAKC,EAAC;AAAA,MACjB;AACA,eAAS,EAAEH,IAAG;AACZ,gBACIA,KAAIA,MAAK,CAAC,GAAG,QAAQ,EAAE,OACxBA,GAAE,cAAc,EAAE,OAClBA,GAAE,iBAAiB,QACnBA,GAAE,kBAAkB,EAAE,YAAY,IAClCA,GAAE,iBAAiB,EAAE,UACrBA,GAAE,kBAAkB,EAAE,WACvBA;AAAA,MAEJ;AACA,eAAS,EAAEA,IAAG;AACZ,eAAO;AAAA,UACL,WAAWA,GAAE,YAAY;AAAA,UACzB,UAAUA,GAAE;AAAA,UACZ,SAASA,GAAE;AAAA,UACX,SAASA,GAAE;AAAA,UACX,UAAUA,GAAE,YAAY;AAAA,QAC1B;AAAA,MACF;AACA,eAAS,EAAEA,IAAGC,IAAGE,IAAG;AAClB,QAAC,wBAAwBH,MAAK,uBAAuBA,OAClDC,GAAE,UAAUA,GAAE,WACf,OAAOA,GAAE,UACT,OAAOA,GAAE,UACT,OAAOA,GAAE;AACX,YAAIC,KAAID,GAAE;AACV,QAAAC,OACIA,KAAID,GAAE,SACR,OAAOA,GAAE,SACRC,KAAK,SAASF,IAAGC,IAAG;AACnB,cAAIE,KAAIH,IACNE,KAAI,EAAEC,EAAC;AACT,UAAAD,OAAMC,KAAID;AACV,cAAIE,KAAI;AACR,cAAIH,IAAG;AACL,gBAAII,KAAIJ,GAAE,QAAQ,GAAG;AACrB,0BAAcG,KAAIH,GAAE,UAAUI,KAAI,CAAC,OAAOD,KAAI,OAC5C,YAAYA,OAAMA,KAAI,SACtB,MAAMA,GAAE,QAAQ,SAAS,MAAMA,KAAIA,GAAE,UAAU,CAAC,IAChD,MAAMA,GAAE,QAAQ,OAAO,MAAMA,KAAIA,GAAE,UAAU,CAAC,IAC7C,oBACEA,MAAKA,KAAIA,GAAE,QAAQ,MAAM,GAAG,GAAG,YAAY,MAC5C,8BAA8BA,OAC7BA,KAAI,sBACP,YAAYD,MAAK,wBAAwBC,OAAMA,KAAI,OACnD,MAAMA,OAAMA,KAAI;AAAA,UACpB;AACA,iBAAQH,KAAIE,KAAI,MAAMC;AAAA,QACxB,EAAGJ,IAAGE,EAAC,GACND,GAAE,SAASC,MACXC,KAAIA,MAAK,CAAC,GAAG,cAAcA,GAAE,UAAUF,EAAC,GAAG,OAAOE,GAAE,YACpDD,KAAID,GAAE,UAAU,IACjB,EAAE,SAAS,CAACE,GAAE,iBAAiB,MAAM,KAAK,UAAUF,EAAC,CAAC;AACxD,YAAIG,KAAIF,GAAE,QAAQ,GAAG;AACrB,gBAAQA,GAAE,UAAUE,KAAI,CAAC,GAAG;AAAA,UAC1B,KAAK;AACH,YAAAD,GAAE,WAAWA,GAAE,QAAQF,EAAC;AACxB;AAAA,UACF,KAAK;AACH,YAAAE,GAAE,UAAUA,GAAE,OAAOF,EAAC;AACtB;AAAA,UACF;AACE,YAAAE,GAAE,QAAQA,GAAE,KAAKF,EAAC;AAAA,QACtB;AACA,QAAAE,GAAE,YAAYA,GAAE,SAASF,EAAC;AAAA,MAC5B;AACA,eAAS,EAAED,IAAG;AACZ,YAAIA,IAAG;AACL,mBAASC,KAAI,GAAGE,KAAIH,GAAE,QAAQC,KAAIE,IAAG,EAAEF,IAAG;AACxC,gBAAIC,KAAIF,GAAEC,EAAC,GACTG,KAAI,EAAEF,EAAC;AACT,YAAAE,OAAMJ,GAAEC,EAAC,IAAIG;AAAA,UACf;AACA,iBAAOJ;AAAA,QACT;AAAA,MACF;AACA,eAAS,EAAEA,IAAGC,IAAG;AACf,YAAI,EAAE,CAAC,EAAE,SAAUA,MAAKA,GAAE,gBAAiB;AACzC,cAAIE,KAAI,EAAEH,EAAC;AACX,UAAAG,OAAMH,KAAIG,KACRF,MAAKA,GAAE,aAAa,OAAOA,GAAE,WAC7B,QAAQ,IAAI,MAAMD,KAAI,MAAMC,MAAK,EAAE;AAAA,QACvC;AAAA,MACF;AACA,eAAS,IAAI;AACX,gBAAO,oBAAI,KAAK,GAAE,QAAQ;AAAA,MAC5B;AACA,eAAS,EAAED,IAAG;AACZ,cACG,EAAE,iBACCA,GAAE,IACF,EAAE,oBACF,EAAE,iBAAiB,uBAAuBA,IAAG,KAAE;AAAA,MACvD;AAAA,IACF,CAAC;AAAA;AAAA;", "names": ["e", "n", "t", "i", "o", "r", "a"]}