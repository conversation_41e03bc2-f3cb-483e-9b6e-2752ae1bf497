<?php

namespace app\shop\model\order;

use app\common\model\order\Order as OrderModel;
use app\common\enum\order\OrderTypeEnum;
use app\common\service\message\MessageService;
use app\common\service\order\OrderRefundService;
use app\common\enum\order\OrderPayStatusEnum;
use app\common\service\product\factory\ProductFactory;
use app\shop\service\order\ExportService;
use app\common\model\settings\Setting as SettingModel;
use app\common\service\order\OrderCompleteService;

/**
 * 订单模型
 */
class Order extends OrderModel
{
    /**
     * 订单列表
     */
    public function getList($dataType, $data = null)
    {
        // 店内订单使用主订单显示
        if (isset($data['order_type']) && $data['order_type'] == 1) {
            return $this->getStoreOrderList($dataType, $data);
        }

        // 其他订单类型使用原逻辑
        $model = $this;
        $model = $model->setWhere($model, $data);
        $whereConditions = $this->transferDataType($dataType);

        return $model->with(['product' => ['image'], 'user', 'supplier'])
            ->order(['create_time' => 'desc'])
            ->where($whereConditions)
            ->paginate($data);
    }

    /**
     * 获取店内订单列表（按主订单显示）
     */
    public function getStoreOrderList($dataType, $data)
    {
        $masterOrderModel = new \app\common\model\order\MasterOrder();

        // 基础查询条件
        $query = $masterOrderModel->where('is_delete', 0);

        // 门店筛选
        if (!empty($data['shop_supplier_id'])) {
            $query->where('shop_supplier_id', $data['shop_supplier_id']);
        }

        // 状态筛选
        switch ($dataType) {
            case 'dining':
                $query->where('order_status', 10); // 用餐中
                break;
            case 'complete':
                $query->where('order_status', 30); // 已完成
                break;
            case 'cancel':
                $query->whereIn('order_status', [40, 50]); // 已取消
                break;
            case 'all':
            default:
                // 显示所有
                break;
        }

        // 获取主订单，关联桌位信息
        $result = $query->with(['table'])
            ->order('create_time', 'desc')
            ->paginate($data);

        // 为每个主订单添加统计信息
        foreach ($result->items() as $masterOrder) {
            // 获取子订单数量
            $subOrderCount = $this->where('master_order_id', $masterOrder->master_order_id)
                ->where('is_sub_order', 1)
                ->count();

            // 获取商品总数量
            $totalProductCount = \think\facade\Db::table('jjjfood_order')
                ->alias('o')
                ->join('jjjfood_order_product op', 'o.order_id = op.order_id')
                ->where('o.master_order_id', $masterOrder->master_order_id)
                ->where('o.is_sub_order', 1)
                ->sum('op.total_num');

            // 添加到主订单对象
            $masterOrder->sub_order_count = $subOrderCount;
            $masterOrder->total_product_count = $totalProductCount ?: 0;

            // 安全地获取状态值
            $orderStatus = is_array($masterOrder->order_status) ? $masterOrder->order_status['value'] : $masterOrder->order_status;
            $payStatus = is_array($masterOrder->pay_status) ? $masterOrder->pay_status['value'] : $masterOrder->pay_status;

            $masterOrder->order_status_text = $this->getOrderStatusText($orderStatus);
            $masterOrder->pay_status_text = $this->getPayStatusText($payStatus);

            // 直接查询桌位信息
            $tableModel = new \app\common\model\store\Table();
            $table = $tableModel->where('table_id', $masterOrder->table_id)->find();

            $masterOrder->table_no = $table ? $table->table_no : '未知桌位';
            $masterOrder->is_master_row = true;
        }

        return $result;
    }

    /**
     * 获取订单状态文本
     */
    public function getOrderStatusText($status = null)
    {
        // 如果没有传入状态，从当前对象获取
        if ($status === null) {
            $status = $this->getData('order_status');
        }

        $statusMap = [
            10 => '用餐中',
            20 => '待结账',
            30 => '已完成',
            40 => '已取消',
            50 => '异常关台'
        ];
        return $statusMap[$status] ?? '未知';
    }

    /**
     * 获取支付状态文本
     */
    public function getPayStatusText($status = null)
    {
        // 如果没有传入状态，从当前对象获取
        if ($status === null) {
            $status = $this->getData('pay_status');
        }

        $statusMap = [
            10 => '未结账',
            20 => '已结账'
        ];
        return $statusMap[$status] ?? '未知';
    }

    /**
     * 获取订单总数
     */
    public function getCount($type, $data)
    {
        $model = $this;
        // 检索查询条件
        $model = $model->setWhere($model, $data);
        if ($type == 'refund') {
            $model = $model->where('refund_money', '>', 0);
        }
        // 获取数据列表
        return $model->alias('order')
            ->where($this->transferDataType($type))
            ->count();
    }

    /**
     * 订单列表(全部)
     */
    public function getListAll($dataType, $query = [])
    {
        $model = $this;
        // 检索查询条件
        $model = $model->setWhere($model, $query);
        // 获取数据列表
        return $model->with(['product.image', 'address', 'user', 'extract'])
            ->alias('order')
            ->field('order.*')
            ->where($this->transferDataType($dataType))
            ->where('order.is_delete', '=', 0)
            ->order(['order.create_time' => 'desc'])
            ->select();
    }

    /**
     * 订单导出
     */
    public function exportList($dataType, $query)
    {
        // 获取订单列表
        $list = $this->getListAll($dataType, $query);
        // 导出excel文件
        return (new Exportservice)->orderList($list);
    }

    /**
     * 设置检索查询条件
     */
    private function setWhere($model, $data)
    {
        //搜索订单号
        if (isset($data['order_no']) && $data['order_no'] != '') {
            $model = $model->where('order_no', 'like', '%' . trim($data['order_no']) . '%');
        }
        //搜索配送方式
        if (isset($data['style_id']) && $data['style_id'] != '') {
            $model = $model->where('delivery_type', '=', $data['style_id']);
        }
        //搜索订单类型
        if (isset($data['order_type'])) {
            $model = $model->where('order_type', '=', $data['order_type']);
        }
        //确保只显示主订单（外卖订单is_sub_order=0，堂食子订单is_sub_order=1）
        $model = $model->where('is_sub_order', '=', 0);
        //搜索配送方式
        if (isset($data['shop_supplier_id']) && $data['shop_supplier_id']) {
            $model = $model->where('shop_supplier_id', '=', $data['shop_supplier_id']);
        }
        //搜索时间段
        if (isset($data['create_time']) && $data['create_time'] != '') {
            $model = $model->where('create_time', 'between', [strtotime($data['create_time'][0]), strtotime($data['create_time'][1]) + 86399]);
        }
        //搜索支付时间段
        if (isset($data['dataType']) && $data['dataType'] == 'refund') {
            $model = $model->where('refund_money', '>', 0);
        }
        return $model;
    }

    /**
     * 转义数据类型条件
     */
    private function transferDataType($dataType)
    {
        $filter = [];
        // 订单数据类型
        switch ($dataType) {
            case 'all':
                // 显示所有订单（is_sub_order在setWhere中统一处理）
                break;
            case 'dining':
                // 用餐中：显示进行中的订单
                $filter['order_status'] = 10; // 进行中
                break;
            case 'payment';
                $filter['pay_status'] = OrderPayStatusEnum::PENDING;
                $filter['order_status'] = 10;
                break;
            case 'process';
                $filter['pay_status'] = OrderPayStatusEnum::SUCCESS;
//                $filter['delivery_status'] = 10;
                $filter['order_status'] = 10;
                break;
            case 'complete';
                $filter['pay_status'] = OrderPayStatusEnum::SUCCESS;
                $filter['order_status'] = 30;
                break;
            case 'cancel';
//                $filter['pay_status'] = OrderPayStatusEnum::SUCCESS;
                $filter['order_status'] = 20;
                break;
            case 'refund':
                // 已退款：显示有退款金额的订单
                // 这个条件在getCount方法中单独处理
                $filter['order_status'] = [20, 30]; // 取消或完成的订单
                break;
        }
        return $filter;
    }

    /**
     * 发送订单
     * @return bool
     */
    public function sendOrder($order_id)
    {
        $deliver = SettingModel::getSupplierItem('deliver', $this['supplier']['shop_supplier_id']);
        if ($this['order_status']['value'] != 10 || $this['deliver_status'] != 0) {
            $this->error = '订单已发送或已完成';
            return false;
        }
        // 开启事务
        $this->startTrans();
        try {
            $this->addOrder($deliver);
            // 实例化消息通知服务类
            $Service = new MessageService;
            // 发送消息通知
            $Service->delivery($this, OrderTypeEnum::MASTER);
            $this->commit();
            return true;
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            $this->rollback();
            return false;
        }
    }

    /**
     * 取消订单
     */
    public function orderCancel($data)
    {
        // 判断订单是否有效
        if ($this['delivery_status']['value'] == 20 || $this['order_status']['value'] != 10 || $this['pay_status']['value'] != 20) {
            $this->error = "订单不允许取消";
            return false;
        }
        // 订单取消事件
        return $this->transaction(function () use ($data) {
            // 执行退款操作
            $this['pay_type']['value'] < 40 && (new OrderRefundService)->execute($this);
            // 回退商品库存
            ProductFactory::getFactory($this['order_source'])->backProductStock($this['product'], true);
            // 更新订单状态
            return $this->save(['order_status' => 20, 'cancel_remark' => $data['cancel_remark']]);
        });
    }

    /**
     * 审核：用户取消订单
     */
    public function refund($data)
    {
        // 判断订单是否有效
        if ($this['pay_status']['value'] != 20 || $this['order_status']['value'] != 10) {
            $this->error = '该订单不合法';
            return false;
        }
        if ($data['refund_money'] + $this['refund_money'] > $this['pay_price']) {
            $this->error = '退款金额不能超过支付金额';
            return false;
        }
        // 订单取消事件
        $status = $this->transaction(function () use ($data) {
            // 执行退款操作
            $this['pay_type']['value'] < 40 && (new OrderRefundService)->execute($this, $data['refund_money']);
            $deliver = (new OrderDeliver())::detail(['order_id' => $this['order_id'], 'status' => 10]);
            if ($deliver) {
                $deliver->updateDeliver();
            }
            // 更新订单状态：已发货、已收货
            $this->save([
                'delivery_status' => 20,
                'delivery_time' => time(),
                'receipt_status' => 20,
                'receipt_time' => time(),
                'order_status' => 30,
                'refund_money' => $this['refund_money'] + $data['refund_money']
            ]);
            // 执行订单完成后的操作
            $OrderCompleteService = new OrderCompleteService(OrderTypeEnum::MASTER);
            $OrderCompleteService->complete([$this], static::$app_id);
            return true;
        });
        return $status;
    }

    /**
     * 获取待处理订单
     */
    public function getReviewOrderTotal($shop_supplier_id, $order_type)
    {
        $model = $this;
        $filter['pay_status'] = OrderPayStatusEnum::SUCCESS;
        $filter['order_status'] = 10;
        if ($shop_supplier_id) {
            $model = $model->where('shop_supplier_id', '=', $shop_supplier_id);
        }
        if ($order_type >= 0) {
            $model = $model->where('order_type', '=', $order_type);
        }
        return $model->where($filter)->count();
    }

    /**
     * 获取某天的总销售额
     * 结束时间不传则查一天
     */
    public function getOrderTotalPrice($startDate, $endDate, $shop_supplier_id = 0)
    {
        $model = $this;
        $startDate && $model = $model->where('pay_time', '>=', strtotime($startDate));
        if (is_null($endDate) && $startDate) {
            $model = $model->where('pay_time', '<', strtotime($startDate) + 86400);
        } else if ($endDate) {
            $model = $model->where('pay_time', '<', strtotime($endDate) + 86400);
        }
        if ($shop_supplier_id) {
            $model = $model->where('shop_supplier_id', '=', $shop_supplier_id);
        }
        return $model->where('pay_status', '=', 20)
            ->where('order_status', '<>', 20)
            ->where('is_delete', '=', 0)
            ->sum('pay_price');
    }

    /**
     * 获取某天的下单用户数
     */
    public function getPayOrderUserTotal($day, $shop_supplier_id = 0)
    {
        $model = $this;
        $startTime = strtotime($day);
        if ($shop_supplier_id) {
            $model = $model->where('shop_supplier_id', '=', $shop_supplier_id);
        }
        $userIds = $model->distinct(true)
            ->where('pay_time', '>=', $startTime)
            ->where('pay_time', '<', $startTime + 86400)
            ->where('pay_status', '=', 20)
            ->where('is_delete', '=', 0)
            ->column('user_id');
        return count($userIds);
    }

    /**
     * 微信发货
     */
    public function wxDelivery()
    {
        if ($this['pay_source'] != 'wx') {
            $this->error = '当前订单无须发货';
            return false;
        }
        if ($this['wx_delivery_status'] != 10) {
            $this->error = '订单已发货';
            return false;
        }
        $setting = SettingModel::getItem('store');
        if (!$setting['is_send_wx']) {
            $this->error = '未开启小程序发货';
            return false;
        }
        $this->startTrans();
        try {
            // 订单同步到微信
            $result = $this->sendWxExpress();
            if (!$result) {
                return false;
            }
            $this->commit();
            return true;
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            $this->rollback();
            return false;
        }
    }


}