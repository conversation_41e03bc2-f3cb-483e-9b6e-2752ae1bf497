<view data-theme="{{j}}" class="{{k}}"><view class="top-tabbar d-a-c"><view class="{{a}}" bindtap="{{b}}">当前订单</view><view class="{{c}}" bindtap="{{d}}">历史订单</view></view><view class="order-list"><view wx:for="{{e}}" wx:for-item="item" wx:key="q" class="item" catchtap="{{item.r}}"><view class="d-b-c pb14"><text class="order-tips">{{item.a}}</text><view class="item-dianpu flex-1"><view class="item-d-l mr10"><text wx:if="{{item.b}}" class="gray3 f28 fb text-ellipsis">{{item.c}}</text><text wx:if="{{item.d}}" class="gray6 f24"> 桌位：{{item.e}}</text></view></view><view class="state"><text class="gray9 f24">{{item.f}}</text></view></view><view class="product-list pr d-b-c"><view class="o-a flex-1"><view class="list d-s-c pr100"><block wx:for="{{item.g}}" wx:for-item="img"><view wx:if="{{img.a}}" class="cover mr20" key="{{img.f}}"><image src="{{img.b}}" mode="aspectFit"></image><view class="mt10 tc f24 text-ellipsis">{{img.c}}</view><view wx:if="{{img.d}}" class="mt5 tc f20 gray9">{{img.e}}</view></view></block></view></view><view><view class="theme-price f30">￥<text class="f36">{{item.h}}</text></view></view></view><text class="shop-name flex-1">下单时间：{{item.i}}</text><view wx:if="{{item.j}}" class="order-bts"><button wx:if="{{item.k}}" class="default" catchtap="{{item.l}}" type="default">取消订单</button><block wx:if="{{item.m}}"><button class="theme-btn fb" catchtap="{{item.n}}">立即支付</button></block><block wx:if="{{item.o}}"><button class="theme-btn fb" catchtap="{{item.p}}">立即支付</button></block></view></view><view wx:if="{{f}}" class="d-c-c d-c p30"><image style="width:268rpx;height:263rpx;margin-top:123rpx" src="{{g}}" mode="aspectFill"></image><view class="f26 gray9">暂无记录</view><view><button class="btn-normal theme-btn" bindtap="{{h}}">立即点单</button></view></view><uni-load-more wx:else u-i="2b3e936c-0" bind:__l="__l" u-p="{{i||''}}"></uni-load-more></view></view>