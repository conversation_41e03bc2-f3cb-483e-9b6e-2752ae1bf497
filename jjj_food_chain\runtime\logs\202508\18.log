[2025-08-18T11:30:04+08:00][info] 收银台代客点餐 - 桌位状态检查
[2025-08-18T11:30:04+08:00][info] 收银台创建子订单 - 开始
[2025-08-18T11:30:04+08:00][info] 准备创建子订单
[2025-08-18T11:30:04+08:00][info] 子订单数据
[2025-08-18T11:30:04+08:00][info] 子订单创建成功
[2025-08-18T11:30:04+08:00][info] 开始创建订单商品 #0
[2025-08-18T11:30:04+08:00][info] 准备保存订单商品 #0
[2025-08-18T11:30:04+08:00][info] 订单商品 #0 创建成功
[2025-08-18T11:30:04+08:00][info] 开始创建订单商品 #1
[2025-08-18T11:30:04+08:00][info] 准备保存订单商品 #1
[2025-08-18T11:30:04+08:00][info] 订单商品 #1 创建成功
[2025-08-18T11:30:29+08:00][info] 店内订单查询条件
[2025-08-18T11:30:29+08:00][info] 店内订单查询结果
[2025-08-18T11:30:35+08:00][info] 店内订单查询条件
[2025-08-18T11:30:35+08:00][info] 店内订单查询结果
[2025-08-18T11:43:41+08:00][info] 店内订单查询条件
[2025-08-18T11:43:41+08:00][info] 店内订单查询结果
[2025-08-18T11:43:59+08:00][info] 店内订单查询条件
[2025-08-18T11:43:59+08:00][info] 店内订单查询结果
[2025-08-18T11:44:34+08:00][info] 店内订单查询条件
[2025-08-18T11:44:34+08:00][info] 店内订单查询结果
[2025-08-18T11:47:41+08:00][info] 店内订单查询条件
[2025-08-18T11:47:41+08:00][info] 店内订单查询结果
[2025-08-18T11:47:48+08:00][info] 店内订单查询条件
[2025-08-18T11:47:48+08:00][info] 店内订单查询结果
[2025-08-18T11:47:55+08:00][info] 店内订单查询条件
[2025-08-18T11:47:55+08:00][info] 店内订单查询结果
[2025-08-18T11:47:57+08:00][info] 店内订单查询条件
[2025-08-18T11:47:57+08:00][info] 店内订单查询结果
[2025-08-18T11:48:32+08:00][info] 代客点餐 - 桌位状态检查
[2025-08-18T11:48:32+08:00][info] 代客点餐 - 分类数据
[2025-08-18T11:48:32+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T11:48:32+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T11:48:33+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T11:48:33+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T11:48:35+08:00][info] 收银台代客点餐 - 桌位状态检查
[2025-08-18T11:48:35+08:00][info] 收银台创建子订单 - 开始
[2025-08-18T11:48:35+08:00][info] 准备创建子订单
[2025-08-18T11:48:35+08:00][info] 子订单数据
[2025-08-18T11:48:35+08:00][info] 子订单创建成功
[2025-08-18T11:48:35+08:00][info] 开始创建订单商品 #0
[2025-08-18T11:48:35+08:00][info] 准备保存订单商品 #0
[2025-08-18T11:48:35+08:00][info] 订单商品 #0 创建成功
[2025-08-18T11:48:43+08:00][info] 店内订单查询条件
[2025-08-18T11:48:43+08:00][info] 店内订单查询结果
[2025-08-18T11:50:08+08:00][info] 代客点餐 - 桌位状态检查
[2025-08-18T11:50:08+08:00][info] 代客点餐 - 分类数据
[2025-08-18T11:50:08+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T11:50:08+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T11:50:08+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T11:50:08+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T11:50:12+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T11:50:12+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T11:50:17+08:00][info] 收银台代客点餐 - 桌位状态检查
[2025-08-18T11:50:17+08:00][info] 收银台创建子订单 - 开始
[2025-08-18T11:50:17+08:00][info] 准备创建子订单
[2025-08-18T11:50:17+08:00][info] 子订单数据
[2025-08-18T11:50:17+08:00][info] 子订单创建成功
[2025-08-18T11:50:17+08:00][info] 开始创建订单商品 #0
[2025-08-18T11:50:17+08:00][info] 准备保存订单商品 #0
[2025-08-18T11:50:17+08:00][info] 订单商品 #0 创建成功
[2025-08-18T11:50:17+08:00][info] 开始创建订单商品 #1
[2025-08-18T11:50:17+08:00][info] 准备保存订单商品 #1
[2025-08-18T11:50:17+08:00][info] 订单商品 #1 创建成功
[2025-08-18T11:50:37+08:00][info] 店内订单查询条件
[2025-08-18T11:50:37+08:00][info] 店内订单查询结果
[2025-08-18T11:50:40+08:00][info] 店内订单查询条件
[2025-08-18T11:50:40+08:00][info] 店内订单查询结果
[2025-08-18T11:50:50+08:00][info] 代客点餐 - 桌位状态检查
[2025-08-18T11:50:50+08:00][info] 代客点餐 - 分类数据
[2025-08-18T11:50:50+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T11:50:50+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T11:50:50+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T11:50:50+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T11:50:53+08:00][info] 收银台代客点餐 - 桌位状态检查
[2025-08-18T11:50:53+08:00][info] 收银台创建子订单 - 开始
[2025-08-18T11:50:53+08:00][info] 准备创建子订单
[2025-08-18T11:50:53+08:00][info] 子订单数据
[2025-08-18T11:50:53+08:00][info] 子订单创建成功
[2025-08-18T11:50:53+08:00][info] 开始创建订单商品 #0
[2025-08-18T11:50:53+08:00][info] 准备保存订单商品 #0
[2025-08-18T11:50:53+08:00][info] 订单商品 #0 创建成功
[2025-08-18T11:50:55+08:00][info] 店内订单查询条件
[2025-08-18T11:50:55+08:00][info] 店内订单查询结果
[2025-08-18T11:51:03+08:00][info] 店内订单查询条件
[2025-08-18T11:51:03+08:00][info] 店内订单查询结果
[2025-08-18T11:51:50+08:00][info] 店内订单查询条件
[2025-08-18T11:51:50+08:00][info] 店内订单查询结果
[2025-08-18T11:52:28+08:00][info] 店内订单查询条件
[2025-08-18T11:52:28+08:00][info] 店内订单查询结果
[2025-08-18T11:53:20+08:00][info] 店内订单查询条件
[2025-08-18T11:53:20+08:00][info] 店内订单查询结果
[2025-08-18T11:53:29+08:00][info] 店内订单查询条件
[2025-08-18T11:53:29+08:00][info] 店内订单查询结果
[2025-08-18T11:53:38+08:00][info] 店内订单查询条件
[2025-08-18T11:53:38+08:00][info] 店内订单查询结果
[2025-08-18T11:53:39+08:00][info] 店内订单查询条件
[2025-08-18T11:53:39+08:00][info] 店内订单查询结果
[2025-08-18T11:53:40+08:00][info] 店内订单查询条件
[2025-08-18T11:53:40+08:00][info] 店内订单查询结果
[2025-08-18T11:53:41+08:00][info] 店内订单查询条件
[2025-08-18T11:53:41+08:00][info] 店内订单查询结果
[2025-08-18T11:53:43+08:00][info] 店内订单查询条件
[2025-08-18T11:53:43+08:00][info] 店内订单查询结果
[2025-08-18T11:53:44+08:00][info] 店内订单查询条件
[2025-08-18T11:53:44+08:00][info] 店内订单查询结果
[2025-08-18T11:53:44+08:00][info] 店内订单查询条件
[2025-08-18T11:53:44+08:00][info] 店内订单查询结果
[2025-08-18T11:53:44+08:00][info] 店内订单查询条件
[2025-08-18T11:53:44+08:00][info] 店内订单查询结果
[2025-08-18T12:04:39+08:00][info] 主订单查询条件
[2025-08-18T12:04:39+08:00][info] 主订单查询结果
[2025-08-18T12:04:49+08:00][info] 主订单查询条件
[2025-08-18T12:04:49+08:00][info] 主订单查询结果
[2025-08-18T12:04:49+08:00][info] 主订单查询条件
[2025-08-18T12:04:49+08:00][info] 主订单查询结果
[2025-08-18T12:05:23+08:00][info] 代客点餐 - 桌位状态检查
[2025-08-18T12:05:23+08:00][info] 代客点餐 - 分类数据
[2025-08-18T12:05:23+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T12:05:23+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T12:05:24+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T12:05:24+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T12:05:28+08:00][info] 代客点餐 - 桌位状态检查
[2025-08-18T12:05:28+08:00][info] 代客点餐 - 分类数据
[2025-08-18T12:05:28+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T12:05:28+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T12:05:30+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T12:05:30+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T12:05:31+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T12:05:31+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T12:05:34+08:00][info] 代客点餐 - 桌位状态检查
[2025-08-18T12:05:34+08:00][info] 代客点餐 - 分类数据
[2025-08-18T12:05:35+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T12:05:35+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T12:05:35+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T12:05:35+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T12:05:37+08:00][info] 收银台代客点餐 - 桌位状态检查
[2025-08-18T12:05:37+08:00][info] 收银台创建子订单 - 开始
[2025-08-18T12:05:37+08:00][info] 准备创建子订单
[2025-08-18T12:05:37+08:00][info] 子订单数据
[2025-08-18T12:05:37+08:00][info] 子订单创建成功
[2025-08-18T12:05:37+08:00][info] 开始创建订单商品 #0
[2025-08-18T12:05:37+08:00][info] 准备保存订单商品 #0
[2025-08-18T12:05:37+08:00][info] 订单商品 #0 创建成功
[2025-08-18T12:05:39+08:00][info] 主订单查询条件
[2025-08-18T12:05:39+08:00][info] 主订单查询结果
[2025-08-18T12:07:04+08:00][info] 主订单查询条件
[2025-08-18T12:07:04+08:00][info] 主订单查询结果
[2025-08-18T12:47:42+08:00][info] 主订单桌位信息调试
[2025-08-18T12:49:39+08:00][info] 主订单桌位信息调试
[2025-08-18T12:49:40+08:00][info] 主订单桌位信息调试
[2025-08-18T12:49:43+08:00][info] 主订单桌位信息调试
[2025-08-18T12:49:43+08:00][info] 主订单桌位信息调试
[2025-08-18T13:02:04+08:00][info] 代客点餐 - 桌位状态检查
[2025-08-18T13:02:04+08:00][info] 代客点餐 - 分类数据
[2025-08-18T13:02:04+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T13:02:04+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T13:02:04+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T13:02:04+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T13:02:07+08:00][info] 收银台代客点餐 - 桌位状态检查
[2025-08-18T13:02:07+08:00][info] 收银台创建子订单 - 开始
[2025-08-18T13:02:07+08:00][info] 准备创建子订单
[2025-08-18T13:02:07+08:00][info] 子订单数据
[2025-08-18T13:02:07+08:00][info] 子订单创建成功
[2025-08-18T13:02:07+08:00][info] 开始创建订单商品 #0
[2025-08-18T13:02:07+08:00][info] 准备保存订单商品 #0
[2025-08-18T13:02:07+08:00][info] 订单商品 #0 创建成功
[2025-08-18T13:03:42+08:00][info] 代客点餐 - 桌位状态检查
[2025-08-18T13:03:42+08:00][info] 代客点餐 - 分类数据
[2025-08-18T13:03:42+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T13:03:42+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T13:03:45+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T13:03:45+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T13:03:49+08:00][info] 收银台代客点餐 - 桌位状态检查
[2025-08-18T13:03:49+08:00][info] 收银台创建子订单 - 开始
[2025-08-18T13:03:49+08:00][info] 准备创建子订单
[2025-08-18T13:03:49+08:00][info] 子订单数据
[2025-08-18T13:03:49+08:00][info] 子订单创建成功
[2025-08-18T13:03:49+08:00][info] 开始创建订单商品 #0
[2025-08-18T13:03:49+08:00][info] 准备保存订单商品 #0
[2025-08-18T13:03:49+08:00][info] 订单商品 #0 创建成功
[2025-08-18T13:14:18+08:00][info] 代客点餐 - 桌位状态检查
[2025-08-18T13:14:18+08:00][info] 代客点餐 - 分类数据
[2025-08-18T13:14:18+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T13:14:18+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T13:14:19+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T13:14:19+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T13:14:23+08:00][info] 收银台代客点餐 - 桌位状态检查
[2025-08-18T13:14:23+08:00][info] 收银台创建子订单 - 开始
[2025-08-18T13:14:23+08:00][info] 准备创建子订单
[2025-08-18T13:14:23+08:00][info] 子订单数据
[2025-08-18T13:14:23+08:00][info] 子订单创建成功
[2025-08-18T13:14:23+08:00][info] 开始创建订单商品 #0
[2025-08-18T13:14:23+08:00][info] 准备保存订单商品 #0
[2025-08-18T13:14:23+08:00][info] 订单商品 #0 创建成功
[2025-08-18T13:14:28+08:00][info] 代客点餐 - 桌位状态检查
[2025-08-18T13:14:28+08:00][info] 代客点餐 - 分类数据
[2025-08-18T13:14:28+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T13:14:28+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T13:14:29+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T13:14:29+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T13:14:35+08:00][info] 收银台代客点餐 - 桌位状态检查
[2025-08-18T13:14:35+08:00][info] 收银台创建子订单 - 开始
[2025-08-18T13:14:35+08:00][info] 准备创建子订单
[2025-08-18T13:14:35+08:00][info] 子订单数据
[2025-08-18T13:14:35+08:00][info] 子订单创建成功
[2025-08-18T13:14:35+08:00][info] 开始创建订单商品 #0
[2025-08-18T13:14:35+08:00][info] 准备保存订单商品 #0
[2025-08-18T13:14:35+08:00][info] 订单商品 #0 创建成功
[2025-08-18T13:17:09+08:00][info] 代客点餐 - 桌位状态检查
[2025-08-18T13:17:09+08:00][info] 代客点餐 - 分类数据
[2025-08-18T13:17:10+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T13:17:10+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T13:17:11+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T13:17:11+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T13:17:12+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T13:17:12+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T13:28:32+08:00][info] 代客点餐 - 桌位状态检查
[2025-08-18T13:28:32+08:00][info] 代客点餐 - 分类数据
[2025-08-18T13:28:32+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T13:28:32+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T13:28:32+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T13:28:32+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T13:28:35+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T13:28:35+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T13:28:41+08:00][info] 收银台代客点餐 - 桌位状态检查
[2025-08-18T13:28:41+08:00][info] 收银台创建子订单 - 开始
[2025-08-18T13:28:41+08:00][info] 准备创建子订单
[2025-08-18T13:28:41+08:00][info] 子订单数据
[2025-08-18T13:28:41+08:00][info] 子订单创建成功
[2025-08-18T13:28:41+08:00][info] 开始创建订单商品 #0
[2025-08-18T13:28:41+08:00][info] 准备保存订单商品 #0
[2025-08-18T13:28:41+08:00][info] 订单商品 #0 创建成功
[2025-08-18T13:28:41+08:00][info] 开始创建订单商品 #1
[2025-08-18T13:28:41+08:00][info] 准备保存订单商品 #1
[2025-08-18T13:28:41+08:00][info] 订单商品 #1 创建成功
[2025-08-18T13:53:12+08:00][info] \EasyWeChat\Kernel\HttpClient\Response::__set_state(array(
   'response' => 
  \Symfony\Component\HttpClient\Response\NativeResponse::__set_state(array(
     'context' => NULL,
     'url' => 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=95_RRCyoV4PDEj2-_jxnfwwSL7fgS3SoWVudMe076NmmXknlIQyUa0tmc_xcKRUC6Ct15vUS0ad_7uCmcXNVUe8kVRLnBHvvyTfp-lPztkmWQPW_L2qmxSKl5qdX3kXIObAIAITZ',
     'resolver' => 
    \Closure::__set_state(array(
    )),
     'onProgress' => NULL,
     'remaining' => NULL,
     'buffer' => NULL,
     'multi' => 
    \Symfony\Component\HttpClient\Internal\NativeClientState::__set_state(array(
       'handlesActivity' => 
      array (
      ),
       'openHandles' => 
      array (
      ),
       'lastTimeout' => NULL,
       'id' => -3882213503276333265,
       'maxHostConnections' => 6,
       'responseCount' => 1,
       'dnsCache' => 
      array (
      ),
       'sleep' => false,
       'hosts' => 
      array (
      ),
    )),
     'pauseExpiry' => 0.0,
     'initializer' => 
    \Closure::__set_state(array(
    )),
     'shouldBuffer' => true,
     'content' => NULL,
     'offset' => 0,
     'jsonData' => NULL,
     'canary' => 
    \Symfony\Component\HttpClient\Internal\Canary::__set_state(array(
       'canceller' => 
      \Closure::__set_state(array(
      )),
    )),
     'headers' => 
    array (
    ),
     'info' => 
    array (
      'response_headers' => 
      array (
      ),
      'url' => 
      array (
        'scheme' => 'https:',
        'authority' => '//api.weixin.qq.com',
        'path' => '/cgi-bin/message/subscribe/send',
        'query' => '?access_token=95_RRCyoV4PDEj2-_jxnfwwSL7fgS3SoWVudMe076NmmXknlIQyUa0tmc_xcKRUC6Ct15vUS0ad_7uCmcXNVUe8kVRLnBHvvyTfp-lPztkmWQPW_L2qmxSKl5qdX3kXIObAIAITZ',
        'fragment' => NULL,
      ),
      'error' => NULL,
      'canceled' => false,
      'http_method' => 'POST',
      'http_code' => 0,
      'redirect_count' => 0,
      'start_time' => 0.0,
      'connect_time' => 0.0,
      'redirect_time' => 0.0,
      'pretransfer_time' => 0.0,
      'starttransfer_time' => 0.0,
      'total_time' => 0.0,
      'namelookup_time' => 0.0,
      'size_upload' => 0,
      'size_download' => 0,
      'size_body' => 330,
      'primary_ip' => '',
      'primary_port' => 443,
      'debug' => '* Enable the curl extension for better performance
',
      'original_url' => 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=95_RRCyoV4PDEj2-_jxnfwwSL7fgS3SoWVudMe076NmmXknlIQyUa0tmc_xcKRUC6Ct15vUS0ad_7uCmcXNVUe8kVRLnBHvvyTfp-lPztkmWQPW_L2qmxSKl5qdX3kXIObAIAITZ',
      'user_data' => NULL,
      'max_duration' => 0.0,
      'pause_handler' => 
      \Closure::__set_state(array(
      )),
    ),
     'handle' => NULL,
     'id' => 321,
     'timeout' => 60.0,
     'inflate' => true,
     'finalInfo' => NULL,
     'logger' => NULL,
  )),
   'failureJudge' => 
  \Closure::__set_state(array(
  )),
   'throw' => true,
))
[2025-08-18T14:25:05+08:00][info] 代客点餐 - 桌位状态检查
[2025-08-18T14:25:05+08:00][info] 代客点餐 - 分类数据
[2025-08-18T14:25:06+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T14:25:06+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T14:25:06+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T14:25:06+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T14:25:11+08:00][info] 收银台代客点餐 - 桌位状态检查
[2025-08-18T14:25:11+08:00][info] 收银台创建子订单 - 开始
[2025-08-18T14:25:11+08:00][info] 准备创建子订单
[2025-08-18T14:25:11+08:00][info] 子订单数据
[2025-08-18T14:25:11+08:00][info] 子订单创建成功
[2025-08-18T14:25:11+08:00][info] 开始创建订单商品 #0
[2025-08-18T14:25:11+08:00][info] 准备保存订单商品 #0
[2025-08-18T14:25:11+08:00][info] 订单商品 #0 创建成功
[2025-08-18T14:39:37+08:00][info] 代客点餐 - 桌位状态检查
[2025-08-18T14:39:37+08:00][info] 代客点餐 - 分类数据
[2025-08-18T14:39:37+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T14:39:37+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T14:39:38+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T14:39:38+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T14:39:41+08:00][info] 收银台代客点餐 - 桌位状态检查
[2025-08-18T14:39:41+08:00][info] 收银台创建子订单 - 开始
[2025-08-18T14:39:41+08:00][info] 准备创建子订单
[2025-08-18T14:39:41+08:00][info] 子订单数据
[2025-08-18T14:39:41+08:00][info] 子订单创建成功
[2025-08-18T14:39:41+08:00][info] 开始创建订单商品 #0
[2025-08-18T14:39:41+08:00][info] 准备保存订单商品 #0
[2025-08-18T14:39:41+08:00][info] 订单商品 #0 创建成功
[2025-08-18T15:45:53+08:00][info] 代客点餐 - 桌位状态检查
[2025-08-18T15:45:53+08:00][info] 代客点餐 - 分类数据
[2025-08-18T15:45:53+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T15:45:53+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T15:45:54+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T15:45:54+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T15:45:58+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T15:45:58+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T15:46:02+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T15:46:02+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T15:46:08+08:00][info] 收银台代客点餐 - 桌位状态检查
[2025-08-18T15:46:08+08:00][info] 收银台创建子订单 - 开始
[2025-08-18T15:46:08+08:00][info] 准备创建子订单
[2025-08-18T15:46:08+08:00][info] 子订单数据
[2025-08-18T15:46:08+08:00][info] 子订单创建成功
[2025-08-18T15:46:08+08:00][info] 开始创建订单商品 #0
[2025-08-18T15:46:08+08:00][info] 准备保存订单商品 #0
[2025-08-18T15:46:08+08:00][info] 订单商品 #0 创建成功
[2025-08-18T15:46:08+08:00][info] 开始创建订单商品 #1
[2025-08-18T15:46:08+08:00][info] 准备保存订单商品 #1
[2025-08-18T15:46:08+08:00][info] 订单商品 #1 创建成功
[2025-08-18T15:48:02+08:00][info] \EasyWeChat\Kernel\HttpClient\Response::__set_state(array(
   'response' => 
  \Symfony\Component\HttpClient\Response\NativeResponse::__set_state(array(
     'context' => NULL,
     'url' => 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=95_RRCyoV4PDEj2-_jxnfwwSL7fgS3SoWVudMe076NmmXknlIQyUa0tmc_xcKRUC6Ct15vUS0ad_7uCmcXNVUe8kVRLnBHvvyTfp-lPztkmWQPW_L2qmxSKl5qdX3kXIObAIAITZ',
     'resolver' => 
    \Closure::__set_state(array(
    )),
     'onProgress' => NULL,
     'remaining' => NULL,
     'buffer' => NULL,
     'multi' => 
    \Symfony\Component\HttpClient\Internal\NativeClientState::__set_state(array(
       'handlesActivity' => 
      array (
      ),
       'openHandles' => 
      array (
      ),
       'lastTimeout' => NULL,
       'id' => -4313791537432255483,
       'maxHostConnections' => 6,
       'responseCount' => 1,
       'dnsCache' => 
      array (
      ),
       'sleep' => false,
       'hosts' => 
      array (
      ),
    )),
     'pauseExpiry' => 0.0,
     'initializer' => 
    \Closure::__set_state(array(
    )),
     'shouldBuffer' => true,
     'content' => NULL,
     'offset' => 0,
     'jsonData' => NULL,
     'canary' => 
    \Symfony\Component\HttpClient\Internal\Canary::__set_state(array(
       'canceller' => 
      \Closure::__set_state(array(
      )),
    )),
     'headers' => 
    array (
    ),
     'info' => 
    array (
      'response_headers' => 
      array (
      ),
      'url' => 
      array (
        'scheme' => 'https:',
        'authority' => '//api.weixin.qq.com',
        'path' => '/cgi-bin/message/subscribe/send',
        'query' => '?access_token=95_RRCyoV4PDEj2-_jxnfwwSL7fgS3SoWVudMe076NmmXknlIQyUa0tmc_xcKRUC6Ct15vUS0ad_7uCmcXNVUe8kVRLnBHvvyTfp-lPztkmWQPW_L2qmxSKl5qdX3kXIObAIAITZ',
        'fragment' => NULL,
      ),
      'error' => NULL,
      'canceled' => false,
      'http_method' => 'POST',
      'http_code' => 0,
      'redirect_count' => 0,
      'start_time' => 0.0,
      'connect_time' => 0.0,
      'redirect_time' => 0.0,
      'pretransfer_time' => 0.0,
      'starttransfer_time' => 0.0,
      'total_time' => 0.0,
      'namelookup_time' => 0.0,
      'size_upload' => 0,
      'size_download' => 0,
      'size_body' => 368,
      'primary_ip' => '',
      'primary_port' => 443,
      'debug' => '* Enable the curl extension for better performance
',
      'original_url' => 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=95_RRCyoV4PDEj2-_jxnfwwSL7fgS3SoWVudMe076NmmXknlIQyUa0tmc_xcKRUC6Ct15vUS0ad_7uCmcXNVUe8kVRLnBHvvyTfp-lPztkmWQPW_L2qmxSKl5qdX3kXIObAIAITZ',
      'user_data' => NULL,
      'max_duration' => 0.0,
      'pause_handler' => 
      \Closure::__set_state(array(
      )),
    ),
     'handle' => NULL,
     'id' => 306,
     'timeout' => 60.0,
     'inflate' => true,
     'finalInfo' => NULL,
     'logger' => NULL,
  )),
   'failureJudge' => 
  \Closure::__set_state(array(
  )),
   'throw' => true,
))
[2025-08-18T16:37:01+08:00][info] 代客点餐 - 桌位状态检查
[2025-08-18T16:37:01+08:00][info] 代客点餐 - 分类数据
[2025-08-18T16:37:01+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T16:37:01+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T16:37:02+08:00][info] 代客点餐 - 商品列表请求
[2025-08-18T16:37:02+08:00][info] 代客点餐 - 商品列表响应
[2025-08-18T16:37:07+08:00][info] 收银台代客点餐 - 桌位状态检查
[2025-08-18T16:37:07+08:00][info] 收银台创建子订单 - 开始
[2025-08-18T16:37:07+08:00][info] 准备创建子订单
[2025-08-18T16:37:07+08:00][info] 子订单数据
[2025-08-18T16:37:07+08:00][info] 子订单创建成功
[2025-08-18T16:37:07+08:00][info] 开始创建订单商品 #0
[2025-08-18T16:37:07+08:00][info] 准备保存订单商品 #0
[2025-08-18T16:37:07+08:00][info] 订单商品 #0 创建成功
[2025-08-18T17:15:53+08:00][info] \EasyWeChat\Kernel\HttpClient\Response::__set_state(array(
   'response' => 
  \Symfony\Component\HttpClient\Response\NativeResponse::__set_state(array(
     'context' => NULL,
     'url' => 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=95_VpNLEC86gwrkac1_1sO4TKTmoQEt5F2mu8ldZTCVr1tYeGjlLodkQGSOU4wWP9Esnvb_zr673g4ygnXm9lGm_dLPsPPQ7g5qMtTkQeiWkUR5I7colGxTghk6jDYWRVaADABWA',
     'resolver' => 
    \Closure::__set_state(array(
    )),
     'onProgress' => NULL,
     'remaining' => NULL,
     'buffer' => NULL,
     'multi' => 
    \Symfony\Component\HttpClient\Internal\NativeClientState::__set_state(array(
       'handlesActivity' => 
      array (
      ),
       'openHandles' => 
      array (
      ),
       'lastTimeout' => NULL,
       'id' => -33257540051277744,
       'maxHostConnections' => 6,
       'responseCount' => 1,
       'dnsCache' => 
      array (
      ),
       'sleep' => false,
       'hosts' => 
      array (
      ),
    )),
     'pauseExpiry' => 0.0,
     'initializer' => 
    \Closure::__set_state(array(
    )),
     'shouldBuffer' => true,
     'content' => NULL,
     'offset' => 0,
     'jsonData' => NULL,
     'canary' => 
    \Symfony\Component\HttpClient\Internal\Canary::__set_state(array(
       'canceller' => 
      \Closure::__set_state(array(
      )),
    )),
     'headers' => 
    array (
    ),
     'info' => 
    array (
      'response_headers' => 
      array (
      ),
      'url' => 
      array (
        'scheme' => 'https:',
        'authority' => '//api.weixin.qq.com',
        'path' => '/cgi-bin/message/subscribe/send',
        'query' => '?access_token=95_VpNLEC86gwrkac1_1sO4TKTmoQEt5F2mu8ldZTCVr1tYeGjlLodkQGSOU4wWP9Esnvb_zr673g4ygnXm9lGm_dLPsPPQ7g5qMtTkQeiWkUR5I7colGxTghk6jDYWRVaADABWA',
        'fragment' => NULL,
      ),
      'error' => NULL,
      'canceled' => false,
      'http_method' => 'POST',
      'http_code' => 0,
      'redirect_count' => 0,
      'start_time' => 0.0,
      'connect_time' => 0.0,
      'redirect_time' => 0.0,
      'pretransfer_time' => 0.0,
      'starttransfer_time' => 0.0,
      'total_time' => 0.0,
      'namelookup_time' => 0.0,
      'size_upload' => 0,
      'size_download' => 0,
      'size_body' => 330,
      'primary_ip' => '',
      'primary_port' => 443,
      'debug' => '* Enable the curl extension for better performance
',
      'original_url' => 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=95_VpNLEC86gwrkac1_1sO4TKTmoQEt5F2mu8ldZTCVr1tYeGjlLodkQGSOU4wWP9Esnvb_zr673g4ygnXm9lGm_dLPsPPQ7g5qMtTkQeiWkUR5I7colGxTghk6jDYWRVaADABWA',
      'user_data' => NULL,
      'max_duration' => 0.0,
      'pause_handler' => 
      \Closure::__set_state(array(
      )),
    ),
     'handle' => NULL,
     'id' => 321,
     'timeout' => 60.0,
     'inflate' => true,
     'finalInfo' => NULL,
     'logger' => NULL,
  )),
   'failureJudge' => 
  \Closure::__set_state(array(
  )),
   'throw' => true,
))
