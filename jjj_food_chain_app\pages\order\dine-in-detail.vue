<template>
	<view :data-theme="theme()" :class="theme() || ''">
		<view class="order-detail" v-if="orderDetail">
			<!-- 订单基本信息 -->
			<view class="order-info">
				<view class="info-header">
					<text class="order-title">堂食订单详情</text>
					<view class="order-status">
						<text class="status-text">{{ orderDetail.order_status_text || '进行中' }}</text>
					</view>
				</view>
				
				<view class="info-content">
					<view class="info-row">
						<text class="label">桌位号：</text>
						<text class="value">{{ orderDetail.table ? orderDetail.table.table_no : '-' }}</text>
					</view>
					<view class="info-row">
						<text class="label">区域：</text>
						<text class="value">{{ orderDetail.table ? orderDetail.table.area_name : '-' }}</text>
					</view>
					<view class="info-row">
						<text class="label">开台时间：</text>
						<text class="value">{{ formatTime(orderDetail.open_time) }}</text>
					</view>
					<view class="info-row">
						<text class="label">用餐时长：</text>
						<text class="value">{{ getDiningDuration(orderDetail.open_time) }}</text>
					</view>
					<view class="info-row">
						<text class="label">我的消费：</text>
						<text class="value amount">￥{{ userTotalAmount }}</text>
					</view>
				</view>
			</view>
			
			<!-- 我的点餐记录 -->
			<view class="sub-orders">
				<view class="section-title">我的点餐记录</view>
				<view class="sub-order-item" v-for="(subOrder, index) in userSubOrders" :key="subOrder.order_id">
					<view class="sub-order-header">
						<text class="order-time">{{ subOrder.order_by || ('第' + (index + 1) + '次点餐') }} - {{ formatTime(subOrder.create_time) }}</text>
						<text class="order-amount">￥{{ subOrder.pay_price }}</text>
					</view>
					
					<!-- 商品列表 -->
					<view class="product-list">
						<view class="product-item" v-for="product in subOrder.product" :key="product.order_product_id">
							<view class="product-image">
								<image :src="product.image ? product.image.file_path : '/static/images/default-product.png'" mode="aspectFill"></image>
							</view>
							<view class="product-info">
								<text class="product-name">{{ product.product_name }}</text>
								<text class="product-attr" v-if="product.product_attr">{{ product.product_attr }}</text>
								<view class="product-price-num">
									<text class="product-price">￥{{ product.product_price }}</text>
									<text class="product-num">x{{ product.total_num }}</text>
								</view>
							</view>
							<view class="product-total">
								<text class="total-price">￥{{ product.total_price }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 加载状态 -->
		<view class="loading" v-if="loading">
			<text>加载中...</text>
		</view>
		
		<!-- 空状态 -->
		<view class="empty" v-if="!loading && !orderDetail">
			<text>订单不存在</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				loading: true,
				masterOrderId: null,
				orderDetail: null,
				userSubOrders: [],
				userTotalAmount: '0.00'
			}
		},
		
		onLoad(options) {
			this.masterOrderId = options.master_order_id;
			if (this.masterOrderId) {
				this.getOrderDetail();
			}
		},
		
		methods: {
			// 获取订单详情
			getOrderDetail() {
				let self = this;
				self.loading = true;
				
				self._get('user.order/dineInDetail', {
					master_order_id: self.masterOrderId
				}, function(result) {
					self.loading = false;
					if (result.data) {
						self.orderDetail = result.data.master_order;
						self.userSubOrders = result.data.user_sub_orders || [];
						self.userTotalAmount = result.data.user_total_amount || '0.00';
					}
				}, function(err) {
					self.loading = false;
					uni.showToast({
						title: '获取订单详情失败',
						icon: 'none'
					});
				});
			},
			
			// 格式化时间
			formatTime(timestamp) {
				if (!timestamp) return '';
				
				let date;
				if (typeof timestamp === 'string') {
					date = new Date(timestamp);
				} else if (typeof timestamp === 'number') {
					if (timestamp.toString().length === 10) {
						date = new Date(timestamp * 1000);
					} else {
						date = new Date(timestamp);
					}
				} else {
					return '';
				}
				
				if (isNaN(date.getTime())) {
					return '';
				}
				
				return date.toLocaleString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit'
				});
			},
			
			// 计算用餐时长
			getDiningDuration(openTime) {
				if (!openTime) return '';
				
				const now = Date.now();
				let start;
				
				if (typeof openTime === 'string') {
					start = new Date(openTime).getTime();
				} else if (typeof openTime === 'number') {
					if (openTime.toString().length === 10) {
						start = openTime * 1000;
					} else {
						start = openTime;
					}
				} else {
					return '';
				}
				
				if (isNaN(start)) return '';
				
				const duration = Math.floor((now - start) / 1000);
				const hours = Math.floor(duration / 3600);
				const minutes = Math.floor((duration % 3600) / 60);
				return `${hours}小时${minutes}分钟`;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.order-detail {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.order-info {
		background: white;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		
		.info-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 30rpx;
			
			.order-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
			}
			
			.order-status {
				.status-text {
					background: #e6f7ff;
					color: #1890ff;
					padding: 8rpx 16rpx;
					border-radius: 20rpx;
					font-size: 24rpx;
				}
			}
		}
		
		.info-content {
			.info-row {
				display: flex;
				justify-content: space-between;
				margin-bottom: 20rpx;
				
				&:last-child {
					margin-bottom: 0;
				}
				
				.label {
					color: #666;
					font-size: 28rpx;
				}
				
				.value {
					color: #333;
					font-size: 28rpx;
					
					&.amount {
						color: #ff4d4f;
						font-weight: bold;
						font-size: 32rpx;
					}
				}
			}
		}
	}
	
	.sub-orders {
		.section-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 20rpx;
		}
		
		.sub-order-item {
			background: white;
			border-radius: 12rpx;
			padding: 30rpx;
			margin-bottom: 20rpx;
			
			.sub-order-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;
				padding-bottom: 20rpx;
				border-bottom: 1px solid #f0f0f0;
				
				.order-time {
					color: #333;
					font-size: 28rpx;
					font-weight: bold;
				}
				
				.order-amount {
					color: #ff4d4f;
					font-size: 28rpx;
					font-weight: bold;
				}
			}
			
			.product-list {
				.product-item {
					display: flex;
					align-items: center;
					margin-bottom: 20rpx;
					
					&:last-child {
						margin-bottom: 0;
					}
					
					.product-image {
						width: 120rpx;
						height: 120rpx;
						border-radius: 8rpx;
						overflow: hidden;
						margin-right: 20rpx;
						
						image {
							width: 100%;
							height: 100%;
						}
					}
					
					.product-info {
						flex: 1;
						
						.product-name {
							display: block;
							font-size: 28rpx;
							color: #333;
							margin-bottom: 8rpx;
						}
						
						.product-attr {
							display: block;
							font-size: 24rpx;
							color: #999;
							margin-bottom: 8rpx;
						}
						
						.product-price-num {
							display: flex;
							justify-content: space-between;
							
							.product-price {
								color: #ff4d4f;
								font-size: 26rpx;
							}
							
							.product-num {
								color: #666;
								font-size: 26rpx;
							}
						}
					}
					
					.product-total {
						.total-price {
							color: #ff4d4f;
							font-size: 28rpx;
							font-weight: bold;
						}
					}
				}
			}
		}
	}
	
	.loading, .empty {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 400rpx;
		color: #999;
		font-size: 28rpx;
	}
</style>
