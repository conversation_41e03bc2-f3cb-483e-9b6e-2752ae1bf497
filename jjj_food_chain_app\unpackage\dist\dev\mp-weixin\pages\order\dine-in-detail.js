"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      loading: true,
      masterOrderId: null,
      orderDetail: null,
      userSubOrders: [],
      userTotalAmount: "0.00"
    };
  },
  onLoad(options) {
    this.masterOrderId = options.master_order_id;
    if (this.masterOrderId) {
      this.getOrderDetail();
    }
  },
  methods: {
    // 获取订单详情
    getOrderDetail() {
      let self = this;
      self.loading = true;
      self._get("user.order/dineInDetail", {
        master_order_id: self.masterOrderId
      }, function(result) {
        self.loading = false;
        if (result.data) {
          self.orderDetail = result.data.master_order;
          self.userSubOrders = result.data.user_sub_orders || [];
          self.userTotalAmount = result.data.user_total_amount || "0.00";
        }
      }, function(err) {
        self.loading = false;
        common_vendor.index.showToast({
          title: "获取订单详情失败",
          icon: "none"
        });
      });
    },
    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp)
        return "";
      let date;
      if (typeof timestamp === "string") {
        date = new Date(timestamp);
      } else if (typeof timestamp === "number") {
        if (timestamp.toString().length === 10) {
          date = new Date(timestamp * 1e3);
        } else {
          date = new Date(timestamp);
        }
      } else {
        return "";
      }
      if (isNaN(date.getTime())) {
        return "";
      }
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit"
      });
    },
    // 计算用餐时长
    getDiningDuration(openTime) {
      if (!openTime)
        return "";
      const now = Date.now();
      let start;
      if (typeof openTime === "string") {
        start = new Date(openTime).getTime();
      } else if (typeof openTime === "number") {
        if (openTime.toString().length === 10) {
          start = openTime * 1e3;
        } else {
          start = openTime;
        }
      } else {
        return "";
      }
      if (isNaN(start))
        return "";
      const duration = Math.floor((now - start) / 1e3);
      const hours = Math.floor(duration / 3600);
      const minutes = Math.floor(duration % 3600 / 60);
      return `${hours}小时${minutes}分钟`;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.orderDetail
  }, $data.orderDetail ? {
    b: common_vendor.t($data.orderDetail.order_status_text || "进行中"),
    c: common_vendor.t($data.orderDetail.table ? $data.orderDetail.table.table_no : "-"),
    d: common_vendor.t($data.orderDetail.table ? $data.orderDetail.table.area_name : "-"),
    e: common_vendor.t($options.formatTime($data.orderDetail.open_time)),
    f: common_vendor.t($options.getDiningDuration($data.orderDetail.open_time)),
    g: common_vendor.t($data.userTotalAmount),
    h: common_vendor.f($data.userSubOrders, (subOrder, index, i0) => {
      return {
        a: common_vendor.t(subOrder.order_by || "第" + (index + 1) + "次点餐"),
        b: common_vendor.t($options.formatTime(subOrder.create_time)),
        c: common_vendor.t(subOrder.pay_price),
        d: common_vendor.f(subOrder.product, (product, k1, i1) => {
          return common_vendor.e({
            a: product.image ? product.image.file_path : "/static/images/default-product.png",
            b: common_vendor.t(product.product_name),
            c: product.product_attr
          }, product.product_attr ? {
            d: common_vendor.t(product.product_attr)
          } : {}, {
            e: common_vendor.t(product.product_price),
            f: common_vendor.t(product.total_num),
            g: common_vendor.t(product.total_price),
            h: product.order_product_id
          });
        }),
        e: subOrder.order_id
      };
    })
  } : {}, {
    i: $data.loading
  }, $data.loading ? {} : {}, {
    j: !$data.loading && !$data.orderDetail
  }, !$data.loading && !$data.orderDetail ? {} : {}, {
    k: _ctx.theme(),
    l: common_vendor.n(_ctx.theme() || "")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e6538b3e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/order/dine-in-detail.js.map
